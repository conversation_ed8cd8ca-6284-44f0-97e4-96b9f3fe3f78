import {
  baseFlatten_default
} from "./chunk-ICPA4G5Z.js";
import {
  baseUniq_default
} from "./chunk-OL3FMKGO.js";
import "./chunk-WOYP34LV.js";
import "./chunk-GBNDIJHH.js";
import {
  last_default
} from "./chunk-7AOFZHRW.js";
import "./chunk-767VLWIT.js";
import "./chunk-6FUVFTHQ.js";
import "./chunk-7DFSKHLR.js";
import {
  isArrayLikeObject_default
} from "./chunk-SODS2ON5.js";
import "./chunk-SXNXAI6R.js";
import "./chunk-KUIRPFKY.js";
import {
  baseRest_default
} from "./chunk-R3LZJF6D.js";
import "./chunk-DQNHI43P.js";
import "./chunk-532EQRVQ.js";
import "./chunk-P5WJJE5X.js";
import "./chunk-SA5Q4YFP.js";
import "./chunk-JFUT5HMH.js";
import "./chunk-KBVNP3C6.js";
import "./chunk-KKDVC4X3.js";
import "./chunk-ZNZP756G.js";
import "./chunk-X3F52GTU.js";
import "./chunk-VO4BPRKV.js";
import "./chunk-VB7E2QJD.js";
import "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/unionWith.js
var unionWith = baseRest_default(function(arrays) {
  var comparator = last_default(arrays);
  comparator = typeof comparator == "function" ? comparator : void 0;
  return baseUniq_default(baseFlatten_default(arrays, 1, isArrayLikeObject_default, true), void 0, comparator);
});
var unionWith_default = unionWith;
export {
  unionWith_default as default
};
//# sourceMappingURL=lodash-es_unionWith.js.map
