import {
  castPath_default
} from "./chunk-FUYQZ2ME.js";
import {
  toKey_default
} from "./chunk-54D7LVNZ.js";
import {
  isIndex_default
} from "./chunk-S5XSWUFE.js";
import {
  isArguments_default
} from "./chunk-KBVNP3C6.js";
import {
  isLength_default
} from "./chunk-KKDVC4X3.js";
import {
  isArray_default
} from "./chunk-VO4BPRKV.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_hasPath.js
function hasPath(object, path, hasFunc) {
  path = castPath_default(path, object);
  var index = -1, length = path.length, result = false;
  while (++index < length) {
    var key = toKey_default(path[index]);
    if (!(result = object != null && hasFunc(object, key))) {
      break;
    }
    object = object[key];
  }
  if (result || ++index != length) {
    return result;
  }
  length = object == null ? 0 : object.length;
  return !!length && isLength_default(length) && isIndex_default(key, length) && (isArray_default(object) || isArguments_default(object));
}
var hasPath_default = hasPath;

export {
  hasPath_default
};
//# sourceMappingURL=chunk-EX7STOUH.js.map
