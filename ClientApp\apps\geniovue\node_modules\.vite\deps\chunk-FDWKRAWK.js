import {
  baseEach_default
} from "./chunk-B5UYKZTA.js";
import {
  isArrayLike_default
} from "./chunk-JFUT5HMH.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseMap.js
function baseMap(collection, iteratee) {
  var index = -1, result = isArrayLike_default(collection) ? Array(collection.length) : [];
  baseEach_default(collection, function(value, key, collection2) {
    result[++index] = iteratee(value, key, collection2);
  });
  return result;
}
var baseMap_default = baseMap;

export {
  baseMap_default
};
//# sourceMappingURL=chunk-FDWKRAWK.js.map
