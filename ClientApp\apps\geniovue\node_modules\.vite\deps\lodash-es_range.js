import {
  toFinite_default
} from "./chunk-XJ7DCSNU.js";
import "./chunk-5XD4SZID.js";
import "./chunk-ZK54QFLC.js";
import {
  isIterateeCall_default
} from "./chunk-HXZRROX4.js";
import "./chunk-532EQRVQ.js";
import "./chunk-S5XSWUFE.js";
import "./chunk-JFUT5HMH.js";
import "./chunk-KKDVC4X3.js";
import "./chunk-ZNZP756G.js";
import "./chunk-X3F52GTU.js";
import "./chunk-VB7E2QJD.js";
import "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseRange.js
var nativeCeil = Math.ceil;
var nativeMax = Math.max;
function baseRange(start, end, step, fromRight) {
  var index = -1, length = nativeMax(nativeCeil((end - start) / (step || 1)), 0), result = Array(length);
  while (length--) {
    result[fromRight ? length : ++index] = start;
    start += step;
  }
  return result;
}
var baseRange_default = baseRange;

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_createRange.js
function createRange(fromRight) {
  return function(start, end, step) {
    if (step && typeof step != "number" && isIterateeCall_default(start, end, step)) {
      end = step = void 0;
    }
    start = toFinite_default(start);
    if (end === void 0) {
      end = start;
      start = 0;
    } else {
      end = toFinite_default(end);
    }
    step = step === void 0 ? start < end ? 1 : -1 : toFinite_default(step);
    return baseRange_default(start, end, step, fromRight);
  };
}
var createRange_default = createRange;

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/range.js
var range = createRange_default();
var range_default = range;
export {
  range_default as default
};
//# sourceMappingURL=lodash-es_range.js.map
