import {
  castPath_default
} from "./chunk-FUYQZ2ME.js";
import {
  toKey_default
} from "./chunk-54D7LVNZ.js";
import "./chunk-OL6NUAZ6.js";
import "./chunk-M6TBIOXS.js";
import "./chunk-ZK54QFLC.js";
import "./chunk-SXNXAI6R.js";
import "./chunk-KUIRPFKY.js";
import {
  assignValue_default
} from "./chunk-KOA24N5T.js";
import "./chunk-3KS2BYTQ.js";
import "./chunk-DQNHI43P.js";
import "./chunk-532EQRVQ.js";
import "./chunk-P5WJJE5X.js";
import {
  isIndex_default
} from "./chunk-S5XSWUFE.js";
import "./chunk-ZNZP756G.js";
import {
  isObject_default
} from "./chunk-X3F52GTU.js";
import "./chunk-VO4BPRKV.js";
import "./chunk-VB7E2QJD.js";
import "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseSet.js
function baseSet(object, path, value, customizer) {
  if (!isObject_default(object)) {
    return object;
  }
  path = castPath_default(path, object);
  var index = -1, length = path.length, lastIndex = length - 1, nested = object;
  while (nested != null && ++index < length) {
    var key = toKey_default(path[index]), newValue = value;
    if (key === "__proto__" || key === "constructor" || key === "prototype") {
      return object;
    }
    if (index != lastIndex) {
      var objValue = nested[key];
      newValue = customizer ? customizer(objValue, key, nested) : void 0;
      if (newValue === void 0) {
        newValue = isObject_default(objValue) ? objValue : isIndex_default(path[index + 1]) ? [] : {};
      }
    }
    assignValue_default(nested, key, newValue);
    nested = nested[key];
  }
  return object;
}
var baseSet_default = baseSet;

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/set.js
function set(object, path, value) {
  return object == null ? object : baseSet_default(object, path, value);
}
var set_default = set;
export {
  set_default as default
};
//# sourceMappingURL=lodash-es_set.js.map
