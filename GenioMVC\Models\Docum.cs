﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;

using CSGenio.business;
using CSGenio.framework;
using CSGenio.persistence;
using GenioMVC.Helpers;
using GenioMVC.Models.Navigation;
using Quidgest.Persistence;
using Quidgest.Persistence.GenericQuery;

using SelectList = Microsoft.AspNetCore.Mvc.Rendering.SelectList;
using JsonIgnoreAttribute = System.Text.Json.Serialization.JsonIgnoreAttribute;

namespace GenioMVC.Models
{
	public class Docum : ModelBase
	{
		[JsonIgnore]
		public CSGenioAdocum klass { get { return baseklass as CSGenioAdocum; } set { baseklass = value; } }

		[Key]
		/// <summary>Field : "" Tipo: "+" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValCoddocum")]
		public string ValCoddocum { get { return klass.ValCoddocum; } set { klass.ValCoddocum = value; } }

		[DisplayName("FKey Descriptive parent")]
		/// <summary>Field : "FKey Descriptive parent" Tipo: "CE" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValCoddscrp")]
		public string ValCoddscrp { get { return klass.ValCoddscrp; } set { klass.ValCoddscrp = value; } }

		private Dscpp _dscpp;
		[DisplayName("Dscpp")]
		[ShouldSerialize("Dscpp")]
		public virtual Dscpp Dscpp
		{
			get
			{
				if (!isEmptyModel && (_dscpp == null || (!string.IsNullOrEmpty(ValCoddscrp) && (_dscpp.isEmptyModel || _dscpp.klass.QPrimaryKey != ValCoddscrp))))
					_dscpp = Models.Dscpp.Find(ValCoddscrp, m_userContext, Identifier, _fieldsToSerialize);
				_dscpp ??= new Models.Dscpp(m_userContext, true, _fieldsToSerialize);
				return _dscpp;
			}
			set { _dscpp = value; }
		}

		[DisplayName("Document")]
		/// <summary>Field : "Document" Tipo: "IB" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValDocum")]
		[Document("ValDocum", true, false, false)]
		public string ValDocum { get { return klass.ValDocum; } set { klass.ValDocum = value; } }
		public string ValDocumfk { get { return klass.ValDocumfk; } set { klass.ValDocumfk = value; } }

		[DisplayName("Path relativo")]
		/// <summary>Field : "Path relativo" Tipo: "C" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValRelpath")]
		public string ValRelpath { get { return klass.ValRelpath; } set { klass.ValRelpath = value; } }

		[DisplayName("Save link to file")]
		/// <summary>Field : "Save link to file" Tipo: "L" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValIslink")]
		public bool ValIslink { get { return Convert.ToBoolean(klass.ValIslink); } set { klass.ValIslink = Convert.ToInt32(value); } }

		[DisplayName("Link to file")]
		/// <summary>Field : "Link to file" Tipo: "C" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValLinkfile")]
		public string ValLinkfile { get { return klass.ValLinkfile; } set { klass.ValLinkfile = value; } }

		[DisplayName("File name")]
		/// <summary>Field : "File name" Tipo: "C" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValDocname")]
		public string ValDocname { get { return klass.ValDocname; } set { klass.ValDocname = value; } }

		[DisplayName("Is folder?")]
		/// <summary>Field : "Is folder?" Tipo: "L" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValIsfolder")]
		public bool ValIsfolder { get { return Convert.ToBoolean(klass.ValIsfolder); } set { klass.ValIsfolder = Convert.ToInt32(value); } }

		[DisplayName("Created by")]
		/// <summary>Field : "Created by" Tipo: "OD" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValCre_date")]
		[DataType(DataType.Date)]
		[DateAttribute("OD")]
		public DateTime? ValCre_date { get { return klass.ValCre_date; } set { klass.ValCre_date = value ?? DateTime.Now;  } }

		[DisplayName("Created by")]
		/// <summary>Field : "Created by" Tipo: "ON" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValCre_user")]
		public string ValCre_user { get { return klass.ValCre_user; } set { klass.ValCre_user = value; } }

		[DisplayName("Amended in")]
		/// <summary>Field : "Amended in" Tipo: "ED" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValUpd_date")]
		[DataType(DataType.Date)]
		[DateAttribute("ED")]
		public DateTime? ValUpd_date { get { return klass.ValUpd_date; } set { klass.ValUpd_date = value ?? DateTime.MinValue;  } }

		[DisplayName("Amended by")]
		/// <summary>Field : "Amended by" Tipo: "EN" Formula:  ""</summary>
		[ShouldSerialize("Docum.ValUpd_user")]
		public string ValUpd_user { get { return klass.ValUpd_user; } set { klass.ValUpd_user = value; } }

		[DisplayName("ZZSTATE")]
		[ShouldSerialize("Docum.ValZzstate")]
		/// <summary>Field: "ZZSTATE", Type: "INT", Formula: ""</summary>
		public virtual int ValZzstate { get { return klass.ValZzstate; } set { klass.ValZzstate = value; } }

		public Docum(UserContext userContext, bool isEmpty = false, string[]? fieldsToSerialize = null) : base(userContext)
		{
			klass = new CSGenioAdocum(userContext.User);
			isEmptyModel = isEmpty;
			if (fieldsToSerialize != null)
				SetFieldsToSerialize(fieldsToSerialize);
		}

		public Docum(UserContext userContext, CSGenioAdocum val, bool isEmpty = false, string[]? fieldsToSerialize = null) : base(userContext)
		{
			klass = val;
			isEmptyModel = isEmpty;
			if (fieldsToSerialize != null)
				SetFieldsToSerialize(fieldsToSerialize);
			FillRelatedAreas(val);
		}

		public void FillRelatedAreas(CSGenioAdocum csgenioa)
		{
			if (csgenioa == null)
				return;

			foreach (RequestedField Qfield in csgenioa.Fields.Values)
			{
				switch (Qfield.Area)
				{
					case "dscpp":
						_dscpp ??= new Dscpp(m_userContext, true, _fieldsToSerialize);
						_dscpp.klass.insertNameValueField(Qfield.FullName, Qfield.Value);
						break;
					default:
						break;
				}
			}
		}

		/// <summary>
		/// Search the row by key.
		/// </summary>
		/// <param name="id">The primary key.</param>
		/// <param name="userCtx">The user context.</param>
		/// <param name="identifier">The identifier.</param>
		/// <param name="fieldsToSerialize">The fields to serialize.</param>
		/// <param name="fieldsToQuery">The fields to query.</param>
		/// <returns>Model or NULL</returns>
		public static Docum Find(string id, UserContext userCtx, string identifier = null, string[] fieldsToSerialize = null, string[] fieldsToQuery = null)
		{
			var record = Find<CSGenioAdocum>(id, userCtx, identifier, fieldsToQuery);
			return record == null ? null : new Docum(userCtx, record, false, fieldsToSerialize) { Identifier = identifier };
		}

		public static List<Docum> AllModel(UserContext userCtx, CriteriaSet args = null, string identifier = null)
		{
			return Where<CSGenioAdocum>(userCtx, false, args, numRegs: -1, identifier: identifier).RowsForViewModel<Docum>((r) => new Docum(userCtx, r));
		}

// USE /[MANUAL TDS MODEL DOCUM]/
	}
}
