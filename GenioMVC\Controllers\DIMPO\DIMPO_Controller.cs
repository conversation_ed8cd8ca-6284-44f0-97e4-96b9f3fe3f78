﻿using JsonPropertyName = System.Text.Json.Serialization.JsonPropertyNameAttribute;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Primitives;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data;
using System.Data.Entity;
using System.Linq;

using CSGenio.business;
using CSGenio.framework;
using CSGenio.persistence;
using CSGenio.reporting;
using GenioMVC.Helpers;
using GenioMVC.Models;
using GenioMVC.Models.Exception;
using GenioMVC.Models.Navigation;
using GenioMVC.Resources;
using GenioMVC.ViewModels;
using GenioMVC.ViewModels.Dimpo;
using GenioServer.business;
using CSGenio.core.ai;

using Quidgest.Persistence.GenericQuery;

//Platform: MVC | Type: INCLUDE_CONTROLLER | Module: TDS | Parameter: DIMPO | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:951cd4c9-cf99-47eb-b6f5-59df44644188
using CSGenio.business.XmlCompliantDocument;
using s1000d_data_importer.Generated;
//END_MANUALCODE

namespace GenioMVC.Controllers
{
	public partial class DimpoController : ControllerBase
	{
		private IChatbotService _aiService;
		public DimpoController(UserContextService userContext, IChatbotService aiService) : base(userContext)
		{
			_aiService = aiService;
		}

// USE /[MANUAL TDS CONTROLLER_NAVIGATION DIMPO]/



		private List<string> GetActionIds(CriteriaSet crs, CSGenio.persistence.PersistentSupport sp = null)
		{
			CSGenio.business.Area area = CSGenio.business.Area.createArea<CSGenioAdimpo>(UserContext.Current.User, UserContext.Current.User.CurrentModule);
			return base.GetActionIds(crs, sp, area);
		}

//Platform: MVCAPI | Type: MANUAL_CONTROLLER | Module: TDS | Parameter: DIMPO | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:2d21d270-64bd-4709-b189-a0cd68cc4587
		//created by [SF] at [2025.04.16]
		//updated by [TCM] on [2025.06.17]
		public class ImportS1000D
		{
			public User _user { get; set; }
			public PersistentSupport _sp { get; set; }
			public ImportS1000D(User user, PersistentSupport sp)
			{
				_user = user;
				_sp = sp;
			}

			public string SendImportS1000D(string coddimpo)
			{
				DataMatrix documents = new GlobalFunctions(_user, _user.CurrentModule).GetValues(new SelectQuery()
					  .Select("DOCUMS", "CODDOCUMS")
					  .From(Area.AreaDIMPO)
					  .Join("docums", "DOCUMS")
					  .On(CriteriaSet.And().Equal("dimpo", "documfk", "DOCUMS", "documid"))
					  .Where(CriteriaSet.And().Equal(CSGenioAdimpo.FldCoddimpo, coddimpo)
					  .NotEqual("DOCUMS", "VERSAO", "CHECKOUT")));
				DBFile file = DbArea.getFileDB(documents.GetString(0, 0), _sp);

				S1000DDocument doc = S1000DDocument.Import(file.File);

                var visitor = doc.MakeVisitor();

                CSGenioAdscpp adscpp = new CSGenioAdscpp(_user);
				visitor.MapAll( // mapeia todos os campos que são 1-1 (sem qualquer lógica, para os quais basta copy-paste direto)
                [
					adscpp
                ]);

                visitor.Run(
                "IdentAndStatusSection.DmAddress.DmIdent.Language", (adscpp, _sp, _user),
                (node, context) =>
                {
                    var lang = (LanguageElemType)node.Node;
                    var (adscpp, sp, user) = ((CSGenioAdscpp, PersistentSupport, User))context;
                    adscpp.ValCodlangu = CSGenioAlangu.searchList(sp, user, CriteriaSet.And().Equal(CSGenioAlangu.FldLanguageisocode, lang.LanguageIsoCode)).FirstOrDefault().ValCodlangu;
                    adscpp.ValCodcount = CSGenioAcount.searchList(sp, user, CriteriaSet.And().Equal(CSGenioAcount.FldCountryisocode, lang.CountryIsoCode)).FirstOrDefault().ValCodcount;
                });

                visitor.Run(
                "IdentAndStatusSection.DmAddress.DmIdent.DmCode", (adscpp, _sp, _user),
                (node, context) =>
                {
                    var code = (DmCodeElemType)node.Node;
                    var (adscpp, sp, user) = ((CSGenioAdscpp, PersistentSupport, User))context;
                    string dmcode = code.SystemCode + "-" + code.SubSystemCode + "-" + code.SubSubSystemCode + "-" + code.AssyCode + "-" +
                                    code.DisassyCode + "-" + code.DisassyCodeVariant + "-" + code.InfoCode + "-" + code.InfoCodeVariant + "-" + code.ItemLocationCode;
                    if (CSGenioAdmcod.searchList(sp, user, CriteriaSet.And().Equal(CSGenioAdmcod.FldCompletecode, dmcode)).Count > 0)
                        adscpp.ValCoddmcod = CSGenioAdmcod.searchList(sp, user, CriteriaSet.And().Equal(CSGenioAdmcod.FldCompletecode, dmcode)).FirstOrDefault().ValCoddmcod;
                });


                _sp.openTransaction();
                adscpp.insertPseud(_sp);
                adscpp.updateDirect(_sp);
                _sp.openTransaction();

                return adscpp.ValCoddscrp;
            }
		}
		public class Dimpo_ImportData
		{
            public string ValCoddimpo { get; set; }
		}
		public ActionResult ImportData([FromBody] Dimpo_ImportData model)
		{
			try
			{
				ImportS1000D importS1000D = new ImportS1000D(m_userContext.User, m_userContext.PersistentSupport);
				string coddscrp = importS1000D.SendImportS1000D(model.ValCoddimpo);
				return Json(new { success = "OK", message = "", id = coddscrp });
			}
			catch (Exception ex)
			{
				return Json(new { success = "E", message = Resources.Resources.ERROR_IMPORT_FILE44889 });
			}
			//return Json(new { success = "OK", id = model.ValCoddimpo });
		}
//END_MANUALCODE





		/// <summary>
		/// Recalculate formulas of the "Dimpo" form. (++, CT, SR, CL and U1)
		/// </summary>
		/// <param name="formData">Current form data</param>
		/// <returns></returns>
		[HttpPost]
		public JsonResult RecalculateFormulas_Dimpo([FromBody]Dimpo_ViewModel formData)
		{
			return GenericRecalculateFormulas(formData, "dimpo",
				(primaryKey) => Models.Dimpo.Find(primaryKey, UserContext.Current, "FDIMPO"),
				(model) => formData.MapToModel(model as Models.Dimpo)
			);
		}

		/// <summary>
		/// Get "See more..." tree structure
		/// </summary>
		/// <returns></returns>
		public JsonResult GetTreeSeeMore([FromBody]RequestLookupModel requestModel)
		{
			var Identifier = requestModel.Identifier;
			var queryParams = requestModel.QueryParams;

			try
			{
				// We need the request values to apply filters
				var requestValues = new NameValueCollection();
				if (queryParams != null)
					foreach (var kv in queryParams)
						requestValues.Add(kv.Key, kv.Value);

				switch (string.IsNullOrEmpty(Identifier) ? "" : Identifier)
				{
					default:
						break;
				}
			}
			catch (Exception)
			{
				return Json(new { Success = false, Message = "Error" });
			}

			return Json(new { Success = false, Message = "Error" });
		}

		/// <summary>
		/// Gets the necessary tickets to interact with the given document
		/// </summary>
		/// <param name="requestModel">The request model with the table, field and the primary key of the record</param>
		/// <returns>A JSON response with the result of the operation</returns>
		public ActionResult GetDocumsTickets([FromBody] RequestDocumGetTicketsModel requestModel)
		{
			return base.GetDocumsTickets("DIMPO", requestModel.FieldName, requestModel.KeyValue);
		}

		/// <summary>
		/// Gets the versions of the specified document
		/// </summary>
		/// <param name="requestModel">The request model with the ticket</param>
		/// <returns>A JSON response with the result of the operation</returns>
		public ActionResult GetFileVersions([FromBody] RequestDocumGetModel requestModel)
		{
			return base.GetFileVersions(requestModel.Ticket);
		}

		/// <summary>
		/// Gets the properties of the specified document
		/// </summary>
		/// <param name="requestModel">The request model with the ticket</param>
		/// <returns>A JSON response with the result of the operation</returns>
		public ActionResult GetFileProperties([FromBody] RequestDocumGetModel requestModel)
		{
			return base.GetFileProperties(requestModel.Ticket);
		}

		/// <summary>
		/// Gets the binary file associated to the specified document
		/// </summary>
		/// <param name="requestModel">The request model with the ticket and view type</param>
		/// <returns>A File object with the content of the document</returns>
		public ActionResult GetFile([FromBody] RequestDocumGetModel requestModel)
		{
			return base.GetFile(requestModel.Ticket, requestModel.ViewType);
		}

		/// <summary>
		/// Stores a new document in the Docums table
		/// </summary>
		/// <param name="requestModel">The request model with the document and ticket</param>
		/// <returns>A JSON response with the result of the operation</returns>
		public ActionResult SetFile([FromForm] RequestDocumsCreateModel requestModel)
		{
			return base.SetFile(requestModel.Ticket, requestModel.Mode, requestModel.Version);
		}

		/// <summary>
		/// Changes the state/properties of a given document
		/// </summary>
		/// <param name="requestModel">The request model with a list of changes</param>
		/// <returns>A JSON response with the result of the operation</returns>
		public ActionResult SetFilesState([FromBody] RequestDocumsChangeModel requestModel)
		{
			return base.SetFilesState(requestModel.Documents);
		}
	}
}
