{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseForOwn.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_createBaseEach.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseEach.js"], "sourcesContent": ["import baseFor from './_baseFor.js';\nimport keys from './keys.js';\n\n/**\n * The base implementation of `_.forOwn` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\nfunction baseForOwn(object, iteratee) {\n  return object && baseFor(object, iteratee, keys);\n}\n\nexport default baseForOwn;\n", "import isArrayLike from './isArrayLike.js';\n\n/**\n * Creates a `baseEach` or `baseEachRight` function.\n *\n * @private\n * @param {Function} eachFunc The function to iterate over a collection.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseEach(eachFunc, fromRight) {\n  return function(collection, iteratee) {\n    if (collection == null) {\n      return collection;\n    }\n    if (!isArrayLike(collection)) {\n      return eachFunc(collection, iteratee);\n    }\n    var length = collection.length,\n        index = fromRight ? length : -1,\n        iterable = Object(collection);\n\n    while ((fromRight ? index-- : ++index < length)) {\n      if (iteratee(iterable[index], index, iterable) === false) {\n        break;\n      }\n    }\n    return collection;\n  };\n}\n\nexport default createBaseEach;\n", "import baseForOwn from './_baseForOwn.js';\nimport createBaseEach from './_createBaseEach.js';\n\n/**\n * The base implementation of `_.forEach` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n */\nvar baseEach = createBaseEach(baseForOwn);\n\nexport default baseEach;\n"], "mappings": ";;;;;;;;;;;AAWA,SAAS,WAAW,QAAQ,UAAU;AACpC,SAAO,UAAU,gBAAQ,QAAQ,UAAU,YAAI;AACjD;AAEA,IAAO,qBAAQ;;;ACLf,SAAS,eAAe,UAAU,WAAW;AAC3C,SAAO,SAAS,YAAY,UAAU;AACpC,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,IACT;AACA,QAAI,CAAC,oBAAY,UAAU,GAAG;AAC5B,aAAO,SAAS,YAAY,QAAQ;AAAA,IACtC;AACA,QAAI,SAAS,WAAW,QACpB,QAAQ,YAAY,SAAS,IAC7B,WAAW,OAAO,UAAU;AAEhC,WAAQ,YAAY,UAAU,EAAE,QAAQ,QAAS;AAC/C,UAAI,SAAS,SAAS,KAAK,GAAG,OAAO,QAAQ,MAAM,OAAO;AACxD;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAO,yBAAQ;;;ACpBf,IAAI,WAAW,uBAAe,kBAAU;AAExC,IAAO,mBAAQ;", "names": []}