{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_hasPath.js"], "sourcesContent": ["import castPath from './_castPath.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isIndex from './_isIndex.js';\nimport isLength from './isLength.js';\nimport toKey from './_toKey.js';\n\n/**\n * Checks if `path` exists on `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path to check.\n * @param {Function} hasFunc The function to check properties.\n * @returns {boolean} Returns `true` if `path` exists, else `false`.\n */\nfunction hasPath(object, path, hasFunc) {\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length,\n      result = false;\n\n  while (++index < length) {\n    var key = toKey(path[index]);\n    if (!(result = object != null && hasFunc(object, key))) {\n      break;\n    }\n    object = object[key];\n  }\n  if (result || ++index != length) {\n    return result;\n  }\n  length = object == null ? 0 : object.length;\n  return !!length && isLength(length) && isIndex(key, length) &&\n    (isArray(object) || isArguments(object));\n}\n\nexport default hasPath;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAgBA,SAAS,QAAQ,QAAQ,MAAM,SAAS;AACtC,SAAO,iBAAS,MAAM,MAAM;AAE5B,MAAI,QAAQ,IACR,SAAS,KAAK,QACd,SAAS;AAEb,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,MAAM,cAAM,KAAK,KAAK,CAAC;AAC3B,QAAI,EAAE,SAAS,UAAU,QAAQ,QAAQ,QAAQ,GAAG,IAAI;AACtD;AAAA,IACF;AACA,aAAS,OAAO,GAAG;AAAA,EACrB;AACA,MAAI,UAAU,EAAE,SAAS,QAAQ;AAC/B,WAAO;AAAA,EACT;AACA,WAAS,UAAU,OAAO,IAAI,OAAO;AACrC,SAAO,CAAC,CAAC,UAAU,iBAAS,MAAM,KAAK,gBAAQ,KAAK,MAAM,MACvD,gBAAQ,MAAM,KAAK,oBAAY,MAAM;AAC1C;AAEA,IAAO,kBAAQ;", "names": []}