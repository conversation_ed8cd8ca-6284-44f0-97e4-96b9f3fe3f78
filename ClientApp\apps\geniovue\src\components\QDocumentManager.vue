<template>
	<div class="document-manager">
		<!-- Cabeçalho com estatísticas e ações -->
		<div class="header-section">
			<div class="stats-section">
				<div class="stat-item">
					<i class="fas fa-folder"></i>
					<span>{{ totalFolders }} pasta(s)</span>
				</div>
				<div class="stat-item">
					<i class="fas fa-file"></i>
					<span>{{ totalDocuments }} ficheiro(s)</span>
				</div>
			</div>
			
			<div class="actions-section">
				<button 
					@click="showFolderUpload = !showFolderUpload"
					class="btn btn-primary"
					:class="{ 'active': showFolderUpload }">
					<i class="fas fa-folder-plus"></i>
					{{ showFolderUpload ? 'Ocultar Upload' : 'Upload Pasta' }}
				</button>
				
				<button 
					@click="refreshTree"
					:disabled="loading"
					class="btn btn-secondary">
					<i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
					Atualizar
				</button>
			</div>
		</div>

		<!-- Se<PERSON> de upload de pasta (colapsável) -->
		<div v-if="showFolderUpload" class="upload-section">
			<QFolderUpload
				:parent-id="parentId"
				:parent-table="parentTable"
				@upload-complete="handleUploadComplete"
				@upload-error="handleUploadError" />
		</div>

		<!-- Mensagens de feedback -->
		<div v-if="message" class="message-section">
			<div 
				:class="['alert', message.type === 'success' ? 'alert-success' : 'alert-danger']"
				@click="clearMessage">
				<i :class="message.type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
				{{ message.text }}
				<button class="close-btn">&times;</button>
			</div>
		</div>

		<!-- Árvore de documentos -->
		<div class="tree-section">
			<QMultiFileUploadTree
				ref="documentTree"
				:parent-id="parentId"
				:parent-table="parentTable"
				@move-success="handleMoveSuccess"
				@move-error="handleMoveError" />
		</div>
	</div>
</template>

<script>
import QMultiFileUploadTree from './QMultiFileUploadTree.vue'
import QFolderUpload from './QFolderUpload.vue'

export default {
	name: 'QDocumentManager',

	props: {
		parentId: {
			type: String,
			required: true
		},
		parentTable: {
			type: String,
			default: 'DSCPP'
		}
	},

	components: {
		QMultiFileUploadTree,
		QFolderUpload
	},

	data() {
		return {
			showFolderUpload: false,
			loading: false,
			message: null
		};
	},

	computed: {
		totalDocuments() {
			return this.$refs.documentTree?.totalDocuments || 0;
		},

		totalFolders() {
			return this.$refs.documentTree?.totalFolders || 0;
		}
	},

	methods: {
		async refreshTree() {
			this.loading = true;
			try {
				await this.$refs.documentTree?.loadTreeData();
				this.showMessage('Árvore atualizada com sucesso!', 'success');
			} catch (error) {
				console.error('Erro ao atualizar árvore:', error);
				this.showMessage('Erro ao atualizar árvore: ' + error.message, 'error');
			} finally {
				this.loading = false;
			}
		},

		handleUploadComplete(result) {
			console.log('Upload concluído:', result);
			
			let message = result.message || 'Upload concluído com sucesso!';
			
			// Adicionar detalhes se houver erros
			if (result.errors && result.errors.length > 0) {
				message += ` (${result.errors.length} erro(s) encontrado(s))`;
			}
			
			this.showMessage(message, 'success');
			
			// Recarregar a árvore após upload
			this.refreshTree();
			
			// Ocultar seção de upload após sucesso
			this.showFolderUpload = false;
		},

		handleUploadError(error) {
			console.error('Erro no upload:', error);
			this.showMessage('Erro no upload: ' + (error.message || 'Erro desconhecido'), 'error');
		},

		handleMoveSuccess(result) {
			console.log('Movimento concluído:', result);
			this.showMessage(`Ficheiro "${result.item}" movido para "${result.target}" com sucesso!`, 'success');
		},

		handleMoveError(error) {
			console.error('Erro no movimento:', error);
			this.showMessage(`Erro ao mover "${error.item}": ${error.error}`, 'error');
		},

		showMessage(text, type = 'info') {
			this.message = { text, type };
			
			// Auto-ocultar mensagem após 5 segundos
			setTimeout(() => {
				this.clearMessage();
			}, 5000);
		},

		clearMessage() {
			this.message = null;
		}
	}
};
</script>

<style scoped>
.document-manager {
	width: 100%;
	max-width: 1200px;
	margin: 0 auto;
}

.header-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	background-color: #f8f9fa;
	border-radius: 8px;
	margin-bottom: 20px;
	border: 1px solid #dee2e6;
}

.stats-section {
	display: flex;
	gap: 20px;
}

.stat-item {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 8px 12px;
	background-color: white;
	border-radius: 6px;
	border: 1px solid #dee2e6;
	font-size: 14px;
	color: #495057;
}

.stat-item i {
	color: #007bff;
}

.actions-section {
	display: flex;
	gap: 10px;
}

.btn {
	padding: 10px 16px;
	border: none;
	border-radius: 6px;
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	gap: 8px;
}

.btn-primary {
	background-color: #007bff;
	color: white;
}

.btn-primary:hover {
	background-color: #0056b3;
}

.btn-primary.active {
	background-color: #0056b3;
	box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
}

.btn-secondary {
	background-color: #6c757d;
	color: white;
}

.btn-secondary:hover:not(:disabled) {
	background-color: #545b62;
}

.btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.upload-section {
	margin-bottom: 20px;
	animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
	from {
		opacity: 0;
		transform: translateY(-10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.message-section {
	margin-bottom: 20px;
}

.alert {
	padding: 15px;
	border-radius: 6px;
	display: flex;
	align-items: center;
	gap: 10px;
	cursor: pointer;
	transition: opacity 0.2s ease;
	position: relative;
}

.alert:hover {
	opacity: 0.9;
}

.alert-success {
	background-color: #d4edda;
	border: 1px solid #c3e6cb;
	color: #155724;
}

.alert-danger {
	background-color: #f8d7da;
	border: 1px solid #f5c6cb;
	color: #721c24;
}

.close-btn {
	position: absolute;
	right: 15px;
	background: none;
	border: none;
	font-size: 18px;
	cursor: pointer;
	color: inherit;
	opacity: 0.7;
}

.close-btn:hover {
	opacity: 1;
}

.tree-section {
	background-color: white;
	border-radius: 8px;
	border: 1px solid #dee2e6;
	overflow: hidden;
}

.fa-spin {
	animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
</style>
