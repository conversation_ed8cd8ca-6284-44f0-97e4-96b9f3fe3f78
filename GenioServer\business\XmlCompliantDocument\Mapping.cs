using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlTypes;
using System.DirectoryServices;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Reflection;
using System.Runtime.Serialization.Formatters.Binary;
using System.Security.Principal;
using System.Security.Cryptography.Pkcs;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;

using CSGenio.framework;
using CSGenio.persistence;
using GenioServer.security;
using Quidgest.Persistence;
using Quidgest.Persistence.GenericQuery;

//Platform: CS | Type: IMPORTS | Module: TDS | Parameter:  | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:b4256d83-e1ff-4dd1-b30c-dbf3adf7bead
using System.Diagnostics;
using System.Runtime.InteropServices;
//END_MANUALCODE
// USE /[MANUAL TDS IMPORTS XmlCompliantDocument.Mapping]/

namespace CSGenio.business
{
//Platform: CS | Type: CLASSES | Module: TDS | Parameter: XmlCompliantDocument.Mapping | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:e1ee9554-475a-49bb-9a08-773148a84cb2
    // created by [TCM] on [2025.06.12]
    namespace XmlCompliantDocument
    {
        public readonly struct TableMap
        {
            public DbArea dbArea { get; init; }
            public IEnumerable<(string FieldName, string Path)> fieldMap { get; init; }
        }

        public interface TableMapSource
        {
            public TableMap GetMap(DbArea area);
        }
    }
//END_MANUALCODE

}