import {
  computed,
  getCurrentInstance,
  inject,
  provide,
  ref
} from "./chunk-6UQYPIB4.js";

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/utils/isEmpty.js
function n(r2) {
  return r2 == null ? true : typeof r2 == "string" || Array.isArray(r2) ? r2.length === 0 : typeof r2 == "object" ? Object.keys(r2).length === 0 : false;
}

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/utils/isObject.js
function t(r2) {
  return r2 !== null && typeof r2 == "object" && !Array.isArray(r2);
}

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/utils/merge.js
function i(n3 = {}, c = {}) {
  const o = {}, f2 = /* @__PURE__ */ new Set([...Object.keys(n3), ...Object.keys(c)]);
  for (const t2 of f2) {
    if (t2 === "__proto__" || t2 === "constructor" || t2 === "prototype")
      continue;
    const r2 = n3[t2], e = c[t2];
    t(r2) && t(e) ? o[t2] = i(
      r2,
      e
    ) : o[t2] = e !== void 0 ? e : r2;
  }
  return o;
}

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/composables/defaults.js
var i2 = "q-defaults";
function E() {
  var r2, u2;
  const e = getCurrentInstance();
  if (!e)
    throw new Error("[Quidgest UI] useDefaults must be called from inside a setup function");
  const t2 = e.type.name ?? e.type.__name;
  if (!t2) throw new Error("[Quidgest UI] Could not determine component name");
  const n3 = l(), o = (r2 = n3.value) == null ? void 0 : r2.Global, c = (u2 = n3.value) == null ? void 0 : u2[t2];
  return computed(() => i(o, c));
}
function I(e) {
  if (n(e)) return;
  const t2 = l(), n3 = ref(e), o = computed(() => n(n3.value) ? t2.value : i(t2.value, n3.value));
  provide(i2, o);
}
function l() {
  const e = inject(i2, void 0);
  if (!e) throw new Error("[Quidgest UI] Could not find defaults instance");
  return e;
}

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/utils/color.js
function h(t2, r2 = false) {
  return r2 ? /^#[a-fA-F0-9]{6}$/.test(t2) : /^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/.test(t2);
}
function d(t2) {
  const r2 = t2.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
  if (r2) {
    const n3 = parseInt(r2[1], 10), s2 = parseInt(r2[2], 10), e = parseInt(r2[3], 10);
    return { r: n3, g: s2, b: e };
  }
}
function b(t2) {
  if (h(t2)) {
    t2.length === 4 && (t2 = "#" + t2[1] + t2[1] + t2[2] + t2[2] + t2[3] + t2[3]);
    const r2 = parseInt(t2.slice(1, 3), 16), n3 = parseInt(t2.slice(3, 5), 16), s2 = parseInt(t2.slice(5, 7), 16);
    return { r: r2, g: n3, b: s2 };
  } else {
    const r2 = d(t2);
    if (r2) return r2;
  }
  throw new Error("Invalid color format");
}
function $(t2, r2) {
  const n3 = g(t2), s2 = r2 / 100;
  return n3.l = n3.l - s2 * n3.l, l2(n3);
}
function p(t2) {
  const r2 = t2.r.toString(16).padStart(2, "0"), n3 = t2.g.toString(16).padStart(2, "0"), s2 = t2.b.toString(16).padStart(2, "0");
  return `#${r2}${n3}${s2}`;
}
function M(t2) {
  return `${t2.r} ${t2.g} ${t2.b}`;
}
function g(t2) {
  const r2 = t2.r / 255, n3 = t2.g / 255, s2 = t2.b / 255, e = Math.max(r2, n3, s2), o = Math.min(r2, n3, s2);
  let a3 = 0, i3;
  const c = (e + o) / 2;
  if (e === o)
    a3 = i3 = 0;
  else {
    const u2 = e - o;
    switch (i3 = c > 0.5 ? u2 / (2 - e - o) : u2 / (e + o), e) {
      case r2:
        a3 = (n3 - s2) / u2 + (n3 < s2 ? 6 : 0);
        break;
      case n3:
        a3 = (s2 - r2) / u2 + 2;
        break;
      case s2:
        a3 = (r2 - n3) / u2 + 4;
        break;
    }
    a3 /= 6;
  }
  return {
    h: Math.round(a3 * 360),
    s: Math.round(i3 * 100),
    l: Math.round(c * 100)
  };
}
function l2(t2) {
  const r2 = t2.h / 360, n3 = t2.s / 100, s2 = t2.l / 100;
  let e, o, a3;
  if (n3 === 0)
    e = o = a3 = s2;
  else {
    const i3 = s2 < 0.5 ? s2 * (1 + n3) : s2 + n3 - s2 * n3, c = 2 * s2 - i3;
    e = f(c, i3, r2 + 1 / 3), o = f(c, i3, r2), a3 = f(c, i3, r2 - 1 / 3);
  }
  return {
    r: Math.round(e * 255),
    g: Math.round(o * 255),
    b: Math.round(a3 * 255)
  };
}
function f(t2, r2, n3) {
  return n3 < 0 && (n3 += 1), n3 > 1 && (n3 -= 1), n3 < 1 / 6 ? t2 + (r2 - t2) * 6 * n3 : n3 < 1 / 2 ? r2 : n3 < 2 / 3 ? t2 + (r2 - t2) * (2 / 3 - n3) * 6 : t2;
}
function m(t2) {
  return t2 > 50 ? "#000" : "#fff";
}

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/utils/string.js
function a(e) {
  return e.replace(/([a-z])([A-Z])/g, "$1 $2").replace(/([0-9])([a-zA-Z])/g, "$1 $2").replace(/([a-zA-Z])([0-9])/g, "$1 $2").replace(/([A-Z]+)([A-Z][a-z])/g, "$1 $2").trim().split(/\s+/);
}
function r(e) {
  return a(e).join("-").toLowerCase();
}

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/utils/theme.js
var a2 = "q-theme";
function l3() {
  let e = document.getElementById(
    a2
  );
  return e || (e = document.createElement("style"), e.id = a2, document.head.appendChild(e)), e;
}
function g2(e) {
  let t2 = "";
  for (const c of e) {
    t2 += `.q-theme--${c.name} {
`;
    const m2 = c.scheme;
    let n3;
    for (n3 in m2) {
      const o = m2[n3];
      if (o) {
        t2 += `  ${h2(n3)}: ${o};
`;
        const s2 = b(o);
        t2 += `  ${h2(n3)}-rgb: ${s2.r} ${s2.g} ${s2.b};
`;
      }
    }
    t2 += `}
`;
  }
  const r2 = l3();
  r2.textContent = t2;
}
function h2(e) {
  return e ? `--q-theme-${r(e)}` : "";
}
function E2(e, t2) {
  const r2 = computed(() => {
    const n3 = t2.find((o) => o.name === e.value);
    if (!n3)
      throw new Error(`Theme "${e}" not found`);
    return n3;
  }), c = computed(() => `q-theme--${r2.value.name}`);
  return {
    name: e,
    current: r2,
    themes: t2,
    class: c
  };
}

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/composables/theme.js
var n2 = Symbol.for("q-theme");
function s() {
  const e = inject(n2);
  if (!e)
    throw new Error("[Quidgest UI] Could not find theme instance");
  return e;
}
function u(e) {
  const o = s(), m2 = o.themes, t2 = E2(e, m2);
  return provide(n2, t2), t2;
}

export {
  n,
  i2 as i,
  E,
  I,
  h,
  b,
  $,
  p,
  M,
  g,
  m,
  r,
  g2,
  E2,
  n2,
  s,
  u
};
//# sourceMappingURL=chunk-UT6WI4VU.js.map
