{"version": 3, "sources": ["../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_cloneBuffer.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseCreate.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_getPrototype.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_initCloneObject.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_cloneArrayBuffer.js", "../../../../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_cloneTypedArray.js"], "sourcesContent": ["import root from './_root.js';\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Built-in value references. */\nvar Buffer = moduleExports ? root.Buffer : undefined,\n    allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined;\n\n/**\n * Creates a clone of  `buffer`.\n *\n * @private\n * @param {<PERSON>uffer} buffer The buffer to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Buffer} Returns the cloned buffer.\n */\nfunction cloneBuffer(buffer, isDeep) {\n  if (isDeep) {\n    return buffer.slice();\n  }\n  var length = buffer.length,\n      result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);\n\n  buffer.copy(result);\n  return result;\n}\n\nexport default cloneBuffer;\n", "import isObject from './isObject.js';\n\n/** Built-in value references. */\nvar objectCreate = Object.create;\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = (function() {\n  function object() {}\n  return function(proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object;\n    object.prototype = undefined;\n    return result;\n  };\n}());\n\nexport default baseCreate;\n", "import overArg from './_overArg.js';\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\n\nexport default getPrototype;\n", "import baseCreate from './_baseCreate.js';\nimport getPrototype from './_getPrototype.js';\nimport isPrototype from './_isPrototype.js';\n\n/**\n * Initializes an object clone.\n *\n * @private\n * @param {Object} object The object to clone.\n * @returns {Object} Returns the initialized clone.\n */\nfunction initCloneObject(object) {\n  return (typeof object.constructor == 'function' && !isPrototype(object))\n    ? baseCreate(getPrototype(object))\n    : {};\n}\n\nexport default initCloneObject;\n", "import Uint8Array from './_Uint8Array.js';\n\n/**\n * Creates a clone of `arrayBuffer`.\n *\n * @private\n * @param {ArrayBuffer} arrayBuffer The array buffer to clone.\n * @returns {ArrayBuffer} Returns the cloned array buffer.\n */\nfunction cloneArrayBuffer(arrayBuffer) {\n  var result = new arrayBuffer.constructor(arrayBuffer.byteLength);\n  new Uint8Array(result).set(new Uint8Array(arrayBuffer));\n  return result;\n}\n\nexport default cloneArrayBuffer;\n", "import cloneArrayBuffer from './_cloneArrayBuffer.js';\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\nexport default cloneTypedArray;\n"], "mappings": ";;;;;;;;;;;;;;;;;AAGA,IAAI,cAAc,OAAO,WAAW,YAAY,WAAW,CAAC,QAAQ,YAAY;AAGhF,IAAI,aAAa,eAAe,OAAO,UAAU,YAAY,UAAU,CAAC,OAAO,YAAY;AAG3F,IAAI,gBAAgB,cAAc,WAAW,YAAY;AAGzD,IAAI,SAAS,gBAAgB,aAAK,SAAS;AAA3C,IACI,cAAc,SAAS,OAAO,cAAc;AAUhD,SAAS,YAAY,QAAQ,QAAQ;AACnC,MAAI,QAAQ;AACV,WAAO,OAAO,MAAM;AAAA,EACtB;AACA,MAAI,SAAS,OAAO,QAChB,SAAS,cAAc,YAAY,MAAM,IAAI,IAAI,OAAO,YAAY,MAAM;AAE9E,SAAO,KAAK,MAAM;AAClB,SAAO;AACT;AAEA,IAAO,sBAAQ;;;AC/Bf,IAAI,eAAe,OAAO;AAU1B,IAAI,aAAc,2BAAW;AAC3B,WAAS,SAAS;AAAA,EAAC;AACnB,SAAO,SAAS,OAAO;AACrB,QAAI,CAAC,iBAAS,KAAK,GAAG;AACpB,aAAO,CAAC;AAAA,IACV;AACA,QAAI,cAAc;AAChB,aAAO,aAAa,KAAK;AAAA,IAC3B;AACA,WAAO,YAAY;AACnB,QAAI,SAAS,IAAI;AACjB,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AACF,EAAE;AAEF,IAAO,qBAAQ;;;AC1Bf,IAAI,eAAe,gBAAQ,OAAO,gBAAgB,MAAM;AAExD,IAAO,uBAAQ;;;ACMf,SAAS,gBAAgB,QAAQ;AAC/B,SAAQ,OAAO,OAAO,eAAe,cAAc,CAAC,oBAAY,MAAM,IAClE,mBAAW,qBAAa,MAAM,CAAC,IAC/B,CAAC;AACP;AAEA,IAAO,0BAAQ;;;ACRf,SAAS,iBAAiB,aAAa;AACrC,MAAI,SAAS,IAAI,YAAY,YAAY,YAAY,UAAU;AAC/D,MAAI,mBAAW,MAAM,EAAE,IAAI,IAAI,mBAAW,WAAW,CAAC;AACtD,SAAO;AACT;AAEA,IAAO,2BAAQ;;;ACLf,SAAS,gBAAgB,YAAY,QAAQ;AAC3C,MAAI,SAAS,SAAS,yBAAiB,WAAW,MAAM,IAAI,WAAW;AACvE,SAAO,IAAI,WAAW,YAAY,QAAQ,WAAW,YAAY,WAAW,MAAM;AACpF;AAEA,IAAO,0BAAQ;", "names": []}