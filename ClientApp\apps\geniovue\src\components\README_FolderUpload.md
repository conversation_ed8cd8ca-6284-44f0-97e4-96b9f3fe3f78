# Upload de Pastas - Documentação

## Visão Geral

Esta implementação permite fazer upload de pastas completas (incluindo subpastas) usando a API `webkitdirectory`. Os ficheiros são organizados automaticamente na base de dados com o campo `RELPATH` a refletir a estrutura de pastas.

## Componentes Criados

### 1. `QFolderUpload.vue`
Componente para selecionar e fazer upload de pastas.

**Props:**
- `parentId` (String, obrigatório): ID do registo pai
- `parentTable` (String, default: 'DSCPP'): Tabela pai

**Eventos:**
- `upload-complete`: Emitido quando o upload é concluído com sucesso
- `upload-error`: Emitido quando há erro no upload

### 2. `QDocumentManager.vue`
Componente principal que combina a árvore de documentos com o upload de pastas.

**Props:**
- `parentId` (String, obrigatório): ID do registo pai
- `parentTable` (String, default: 'DSCPP'): Tabela pai

### 3. Backend: `FolderUploadCreate` Action
Nova action no `DOCUM_MultiUploadController.cs` para processar uploads de pastas.

## Como Usar

### Exemplo Básico

```vue
<template>
  <div>
    <QDocumentManager 
      :parent-id="myParentId" 
      parent-table="DSCPP" />
  </div>
</template>

<script>
import QDocumentManager from '@/components/QDocumentManager.vue'

export default {
  components: {
    QDocumentManager
  },
  data() {
    return {
      myParentId: 'SOME_ID'
    }
  }
}
</script>
```

### Exemplo Apenas Upload

```vue
<template>
  <div>
    <QFolderUpload 
      :parent-id="myParentId"
      @upload-complete="handleUploadComplete"
      @upload-error="handleUploadError" />
  </div>
</template>

<script>
import QFolderUpload from '@/components/QFolderUpload.vue'

export default {
  components: {
    QFolderUpload
  },
  data() {
    return {
      myParentId: 'SOME_ID'
    }
  },
  methods: {
    handleUploadComplete(result) {
      console.log('Upload concluído:', result)
      // result.uploadedFiles contém lista de ficheiros carregados
      // result.errors contém lista de erros (se houver)
    },
    
    handleUploadError(error) {
      console.error('Erro no upload:', error)
    }
  }
}
</script>
```

## Estrutura de Dados

### Como o RELPATH é Definido

Quando seleciona uma pasta como `MinhasPastas/Documentos/`, a estrutura fica:

```
MinhasPastas/
├── arquivo1.pdf          → RELPATH: "MinhasPastas"
├── Documentos/
│   ├── doc1.docx         → RELPATH: "MinhasPastas/Documentos"
│   └── Imagens/
│       └── img1.jpg      → RELPATH: "MinhasPastas/Documentos/Imagens"
└── Outros/
    └── outro.txt         → RELPATH: "MinhasPastas/Outros"
```

### Resposta do Backend

```json
{
  "success": true,
  "message": "5 ficheiro(s) carregado(s) com sucesso",
  "uploadedFiles": [
    {
      "documId": "DOC123",
      "fileName": "arquivo1.pdf",
      "relativePath": "MinhasPastas",
      "size": 1024
    },
    {
      "documId": "DOC124", 
      "fileName": "doc1.docx",
      "relativePath": "MinhasPastas/Documentos",
      "size": 2048
    }
  ],
  "errors": [],
  "totalFiles": 5,
  "successCount": 5,
  "errorCount": 0
}
```

## Funcionalidades

### ✅ Implementado
- Upload de pastas com `webkitdirectory`
- Preservação da estrutura de pastas no `RELPATH`
- Preview da estrutura antes do upload
- Progresso do upload
- Tratamento de erros por ficheiro
- Integração com árvore de documentos existente
- Drag & drop entre pastas na árvore
- Atualização automática da árvore após upload

### 🔄 Melhorias Futuras
- Validação de tipos de ficheiro
- Limite de tamanho por pasta
- Compressão automática
- Upload resumível para ficheiros grandes
- Duplicação de pastas existentes

## Compatibilidade

### Browsers Suportados
- ✅ Chrome/Chromium (todas as versões)
- ✅ Firefox 50+
- ✅ Safari 11.1+
- ✅ Edge 79+
- ❌ Internet Explorer (não suportado)

### Limitações
- `webkitdirectory` não funciona em IE
- Alguns browsers móveis podem ter limitações
- Ficheiros ocultos (que começam com .) podem ser ignorados

## Debugging

### Console Logs
O sistema produz logs detalhados:

```javascript
// Frontend
console.log('Pasta base extraída:', baseFolderName)
console.log('Response from FolderUploadCreate:', response)

// Backend  
System.Diagnostics.Debug.WriteLine($"Ficheiro carregado: {fileName} em {relativePath}")
```

### Função de Teste
```javascript
// Na consola do browser
debugTestNullRelpath() // Testa tratamento de RELPATH null
```

## Troubleshooting

### Problema: Árvore não carrega
- Verificar se `parentId` está correto
- Verificar logs da consola para erros de API
- Verificar se há registos com `RELPATH` null (agora tratado automaticamente)

### Problema: Upload falha
- Verificar tamanho dos ficheiros vs. limite do servidor
- Verificar permissões de escrita na base de dados
- Verificar logs do backend para erros específicos

### Problema: Estrutura de pastas incorreta
- Verificar se `baseFolderName` está sendo extraído corretamente
- Verificar função `ExtractRelativePath` no backend
