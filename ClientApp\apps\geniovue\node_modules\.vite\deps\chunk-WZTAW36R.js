import {
  castPath_default
} from "./chunk-FUYQZ2ME.js";
import {
  toKey_default
} from "./chunk-54D7LVNZ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_baseGet.js
function baseGet(object, path) {
  path = castPath_default(path, object);
  var index = 0, length = path.length;
  while (object != null && index < length) {
    object = object[toKey_default(path[index++])];
  }
  return index && index == length ? object : void 0;
}
var baseGet_default = baseGet;

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/get.js
function get(object, path, defaultValue) {
  var result = object == null ? void 0 : baseGet_default(object, path);
  return result === void 0 ? defaultValue : result;
}
var get_default = get;

export {
  baseGet_default,
  get_default
};
//# sourceMappingURL=chunk-WZTAW36R.js.map
