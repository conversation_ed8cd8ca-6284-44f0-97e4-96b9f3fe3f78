using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlTypes;
using System.DirectoryServices;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Reflection;
using System.Runtime.Serialization.Formatters.Binary;
using System.Security.Principal;
using System.Security.Cryptography.Pkcs;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;

using CSGenio.framework;
using CSGenio.persistence;
using GenioServer.security;
using Quidgest.Persistence;
using Quidgest.Persistence.GenericQuery;

//Platform: CS | Type: IMPORTS | Module: TDS | Parameter:  | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:b4256d83-e1ff-4dd1-b30c-dbf3adf7bead
using System.Diagnostics;
using System.Runtime.InteropServices;
//END_MANUALCODE
//Platform: CS | Type: IMPORTS | Module: TDS | Parameter: XmlCompliantDocument.S1000DAdapter | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:92232c33-e54f-43de-8b46-80f0ff1f38ae
using s1000d_data_importer.Generated;
using s1000d_data_importer;
using Type = System.Type;
//END_MANUALCODE

namespace CSGenio.business
{
//Platform: CS | Type: CLASSES | Module: TDS | Parameter: XmlCompliantDocument.S1000DAdapter | File:  | Order: 0
//BEGIN_MANUALCODE_CODMANUA:164d06bf-16b4-42e0-a931-7d1a00bcc310
    // created by [TCM] on [2025.06.12]
    // updated by [TCM] on [2025.06.17]
    namespace XmlCompliantDocument
    {
        public partial class Template
        {
            // TODO: read from file
            public static readonly Template S1000DDataModule = new(@"<?xml version=""1.0"" encoding=""utf-8""?>
<dmodule 
    xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance""
    xsi:noNamespaceSchemaLocation="""">
<identAndStatusSection>
<dmAddress>
    <dmIdent>
    <dmCode/>
    </dmIdent>
</dmAddress>
</identAndStatusSection>
<content/>
</dmodule>");
        }

        public class S1000DDocument : Document<DmoduleElemType>
        {
            public S1000DDocument(Template template) : base(template) {}

            public S1000DDocument(string xml) : base(xml) {}

            private S1000DDocument(Stream stream) : base(new Importer().Import<DmoduleElemType>(stream)) {}

            public static S1000DDocument Import(byte[] file)
            {
                using (MemoryStream stream = new MemoryStream(file))
                {
                    return new S1000DDocument(stream);
                }
            }

            /// <summary>
            /// Checks whether this document complies with the S1000D schema.
            /// </summary>
            public bool IsSchemaCompliant()
            {
                try
                {
                    using (MemoryStream stream = new MemoryStream(Encoding.UTF8.GetBytes(ToString())))
                    {
                        new Importer().Import<DmoduleElemType>(stream);
                    }
                }
                catch
                {
                    return false;
                }

                return true;
            }

            /// <summary>
            /// Checks whether this document complies with its defined BREX business rules.
            /// </summary>
            public bool IsBrexCompliant()
            {
                // TODO: brex compliance
                throw new NotImplementedException();
            }

            public S1000DVisitor MakeVisitor()
            {
                return new S1000DVisitor(this);
            }

            public sealed class S1000DVisitor : Visitor<DmoduleElemType, object>
            {
                private readonly TableMapSource _mappings = new S1000DMap();

                public S1000DVisitor(Document<DmoduleElemType> document) : base(document, "s1000d_data_importer.Generated")
                {
                }

                /// <summary>
                /// Maps the fields from the tree to the database objects for every given area.
                /// Any missing or null values are silently ignored.
                /// </summary>
                public void MapAll(IEnumerable<DbArea> areas)
                {
                    ArgumentNullException.ThrowIfNull(areas);

                    foreach (var area in areas)
                    {
                        var map = _mappings.GetMap(area);
                        var pairs = map.fieldMap?.ToList()
                                    ?? throw new ArgumentException($"The mapping for {area.GetType().Name} returned a null fieldMap", nameof(_mappings));

                        var savedHandlers = new Dictionary<string, Handler>(_handlers);
                        var savedVisited = new HashSet<object>(_visited);
                        var savedAncestors = new List<object>(_ancestors);
                        var savedPropertyStack = new List<PropertyInfo>(_propertyStack);

                        try
                        {
                            _handlers.Clear();

                            foreach (var (fieldName, originalPath) in pairs)
                            {
                                if (string.IsNullOrWhiteSpace(fieldName) || string.IsNullOrWhiteSpace(originalPath))
                                    continue;

                                var trimmed = originalPath.TrimEnd('.');
                                var lastDot = trimmed.LastIndexOf('.');
                                var parentPath = lastDot >= 0 ? trimmed[..lastDot] : "";
                                var childName = lastDot >= 0 ? trimmed[(lastDot + 1)..] : trimmed;

                                var capturedField = fieldName;
                                var capturedChild = childName;
                                var areaForCallback = area;      // keep a stable reference

                                Accept(parentPath, (Context ctx, object userContext) =>
                                {
                                    try
                                    {
                                        if (!ReferenceEquals(userContext, areaForCallback) || userContext is null)
                                            return;

                                        var targetProp = userContext.GetType().GetProperty(capturedField, BindingFlags.Public | BindingFlags.Instance | BindingFlags.FlattenHierarchy);
                                        if (targetProp is null) return;

                                        var childProp = ctx.Node.GetType().GetProperty(capturedChild, BindingFlags.Public | BindingFlags.Instance);
                                        if (childProp is null) return;

                                        var raw = childProp.GetValue(ctx.Node);

                                        if (TryGetEnumerableOfPrimitives(raw, out var seq))
                                        {
                                            var joined = seq is null ? string.Empty : string.Join(Environment.NewLine, seq.Cast<object?>().Select(o => o?.ToString() ?? string.Empty));
                                            targetProp.SetValue(userContext, joined);
                                        }
                                        else
                                        {
                                            var converted = Convert.ChangeType(raw, targetProp.PropertyType);
                                            targetProp.SetValue(userContext, converted);
                                        }
                                    }
                                    catch
                                    {
                                        // TODO: fail-silently
                                        Console.WriteLine($"visitor error in ({originalPath})");
                                    }
                                });
                            }

                            Traverse(reset: false, area);
                        }
                        finally
                        {
                            _handlers.Clear();
                            foreach (var kv in savedHandlers)
                                _handlers[kv.Key] = kv.Value;

                            _visited.Clear();
                            foreach (var n in savedVisited)
                                _visited.Add(n);

                            _ancestors.Clear();
                            _ancestors.AddRange(savedAncestors);

                            _propertyStack.Clear();
                            _propertyStack.AddRange(savedPropertyStack);
                        }
                    }
                }

                static bool IsPrimitiveLike(Type t) => t.IsPrimitive || t == typeof(string) || t == typeof(decimal);

                static bool TryGetEnumerableOfPrimitives(object? value, out IEnumerable? seq)
                {
                    seq = null;

                    if (value is null || value is string) // exclude string which is IEnumerable<char>
                        return false;

                    if (value is not IEnumerable e)
                        return false;

                    Type? elementType = value.GetType().IsArray
                                        ? value.GetType().GetElementType()
                                        : (value.GetType().IsGenericType
                                           ? value.GetType().GetGenericArguments().FirstOrDefault()
                                           : null);

                    elementType ??= e.Cast<object?>().FirstOrDefault()?.GetType();

                    if (elementType is null || !IsPrimitiveLike(elementType))
                        return false;

                    seq = e;
                    return true;
                }
            }
        }

        public class S1000DMap : TableMapSource
        {
            public TableMap GetMap(DbArea area) => area switch
            {
                CSGenioAdscpp dscpp => new TableMap
                {
                    dbArea = area,
                    fieldMap = [
                        // ----- <IdentAndStatusSection> -----
                        ("ValModelidentcode",   "IdentAndStatusSection.DmAddress.DmIdent.DmCode.ModelIdentCode"),
                        ("ValSystemdiffcode",   "IdentAndStatusSection.DmAddress.DmIdent.DmCode.SystemDiffCode"),
                        ("ValTechname",         "IdentAndStatusSection.DmAddress.DmAddressItems.DmTitle.TechName"),
                        ("ValInfoname",         "IdentAndStatusSection.DmAddress.DmAddressItems.DmTitle.InfoName"),
                        // ("ValIssuetype",        "IdentAndStatusSection.DmStatus.IssueType"),
                        ("ValInwork",           "IdentAndStatusSection.DmAddress.DmIdent.IssueInfo.InWork"),
                        ("ValIssuenumber",      "IdentAndStatusSection.DmAddress.DmIdent.IssueInfo.IssueNumber"),
                        ("ValSecurcla",         "IdentAndStatusSection.DmStatus.Security.SecurityClassification"),
                        ("ValSeccocla",         "IdentAndStatusSection.DmStatus.Security.CommercialClassification"),
                        ("ValSeccavat",         "IdentAndStatusSection.DmStatus.Security.Caveat"),
                        ("ValTitle",            "Description.Title"),
                        ("ValSubject",          "Description.Subject"),
                        ("ValCreator",          "Description.Creator"),
                        ("ValContributor",      "Description.Contributor"),
                        ("ValPublisher",        "Description.Publisher"),
                        ("ValType",             "Description.Type"),
                        ("ValFormat",           "Description.Format")
                    ]
                },
                _ => throw new ArgumentException($"Unsupported DbArea type: {area.GetType().FullName}", nameof(area))
            };
        }
    }
//END_MANUALCODE

}