import {
  baseMap_default
} from "./chunk-FDWKRAWK.js";
import {
  baseIteratee_default
} from "./chunk-N4N5G2ZR.js";
import {
  arrayMap_default
} from "./chunk-M6TBIOXS.js";
import {
  isArray_default
} from "./chunk-VO4BPRKV.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/map.js
function map(collection, iteratee) {
  var func = isArray_default(collection) ? arrayMap_default : baseMap_default;
  return func(collection, baseIteratee_default(iteratee, 3));
}
var map_default = map;

export {
  map_default
};
//# sourceMappingURL=chunk-ZMCNODDL.js.map
