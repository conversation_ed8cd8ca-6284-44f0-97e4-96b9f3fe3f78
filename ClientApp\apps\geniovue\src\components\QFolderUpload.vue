<template>
	<div class="folder-upload-container">
		<!-- Botão para selecionar pasta -->
		<div class="upload-section">
			<input
				ref="folderInput"
				type="file"
				webkitdirectory
				directory
				multiple
				@change="handleFolderSelect"
				style="display: none;"
				accept="*/*"
			/>
			
			<button
				@click="selectFolder"
				:disabled="uploading"
				class="btn btn-primary folder-select-btn">
				<i class="fas fa-folder-open"></i>
				{{ uploading ? 'A carregar...' : 'Selecionar Pasta' }}
			</button>
			
			<span v-if="selectedFiles.length > 0" class="file-count">
				{{ selectedFiles.length }} ficheiro(s) selecionado(s)
			</span>
		</div>

		<!-- Preview dos ficheiros selecionados -->
		<div v-if="selectedFiles.length > 0" class="preview-section">
			<h5>Ficheiros a carregar:</h5>
			<div class="file-tree-preview">
				<div v-for="folder in folderStructure" :key="folder.name" class="folder-item">
					<div class="folder-header">
						<i class="fas fa-folder"></i>
						<strong>{{ folder.name }}</strong>
						<span class="file-count-badge">{{ folder.files.length }} ficheiro(s)</span>
					</div>
					<div class="folder-files">
						<div v-for="file in folder.files.slice(0, 5)" :key="file.name" class="file-item">
							<i class="fas fa-file"></i>
							{{ file.name }}
							<span class="file-size">({{ formatFileSize(file.size) }})</span>
						</div>
						<div v-if="folder.files.length > 5" class="more-files">
							... e mais {{ folder.files.length - 5 }} ficheiro(s)
						</div>
					</div>
				</div>
			</div>
			
			<!-- Botões de ação -->
			<div class="action-buttons">
				<button
					@click="uploadFolder"
					:disabled="uploading"
					class="btn btn-success">
					<i class="fas fa-upload"></i>
					{{ uploading ? 'A carregar...' : 'Carregar Pasta' }}
				</button>
				
				<button
					@click="clearSelection"
					:disabled="uploading"
					class="btn btn-secondary">
					<i class="fas fa-times"></i>
					Cancelar
				</button>
			</div>
		</div>

		<!-- Progresso do upload -->
		<div v-if="uploading" class="upload-progress">
			<div class="progress">
				<div 
					class="progress-bar" 
					:style="{ width: uploadProgress + '%' }"
					role="progressbar">
					{{ uploadProgress }}%
				</div>
			</div>
			<div class="upload-status">
				{{ uploadStatus }}
			</div>
		</div>

		<!-- Resultados do upload -->
		<div v-if="uploadResult" class="upload-result">
			<div v-if="uploadResult.success" class="alert alert-success">
				<i class="fas fa-check-circle"></i>
				{{ uploadResult.message }}
				<div v-if="uploadResult.errors && uploadResult.errors.length > 0" class="upload-errors">
					<strong>Erros encontrados:</strong>
					<ul>
						<li v-for="error in uploadResult.errors" :key="error">{{ error }}</li>
					</ul>
				</div>
			</div>
			<div v-else class="alert alert-danger">
				<i class="fas fa-exclamation-circle"></i>
				{{ uploadResult.message }}
			</div>
		</div>
	</div>
</template>

<script>
import { postData } from '@quidgest/clientapp/network'
import { formatFileSize } from './documMultiUploadTree.js'

export default {
	name: 'QFolderUpload',

	props: {
		parentId: {
			type: String,
			required: true
		},
		parentTable: {
			type: String,
			default: 'DSCPP'
		}
	},

	emits: ['upload-complete', 'upload-error'],

	data() {
		return {
			selectedFiles: [],
			folderStructure: [],
			uploading: false,
			uploadProgress: 0,
			uploadStatus: '',
			uploadResult: null,
			baseFolderName: ''
		};
	},

	methods: {
		formatFileSize,

		selectFolder() {
			this.$refs.folderInput.click();
		},

		handleFolderSelect(event) {
			const files = Array.from(event.target.files);
			if (files.length === 0) return;

			this.selectedFiles = files;
			this.uploadResult = null;
			
			// Extrair o nome da pasta base (primeira pasta no caminho)
			this.extractBaseFolderName(files);
			
			// Organizar ficheiros por estrutura de pastas
			this.organizeFolderStructure(files);
		},

		extractBaseFolderName(files) {
			if (files.length === 0) return;
			
			// Pegar o primeiro ficheiro e extrair a pasta base
			const firstFile = files[0];
			const pathParts = firstFile.webkitRelativePath.split('/');
			this.baseFolderName = pathParts[0]; // Primeira pasta é a pasta base
			
			console.log('Pasta base extraída:', this.baseFolderName);
		},

		organizeFolderStructure(files) {
			const folderMap = new Map();

			files.forEach(file => {
				const pathParts = file.webkitRelativePath.split('/');
				const folderPath = pathParts.slice(0, -1).join('/'); // Remover o nome do ficheiro
				
				if (!folderMap.has(folderPath)) {
					folderMap.set(folderPath, {
						name: folderPath,
						files: []
					});
				}
				
				folderMap.get(folderPath).files.push(file);
			});

			this.folderStructure = Array.from(folderMap.values())
				.sort((a, b) => a.name.localeCompare(b.name));
		},

		async uploadFolder() {
			if (this.selectedFiles.length === 0) return;

			this.uploading = true;
			this.uploadProgress = 0;
			this.uploadStatus = 'A preparar ficheiros...';
			this.uploadResult = null;

			try {
				const formData = new FormData();
				
				// Adicionar parâmetros
				formData.append('ParentId', this.parentId);
				formData.append('ParentTable', this.parentTable);
				formData.append('BaseFolderName', this.baseFolderName);

				// Adicionar todos os ficheiros
				this.selectedFiles.forEach((file, index) => {
					formData.append('files', file);
					
					// Atualizar progresso
					const progress = Math.round(((index + 1) / this.selectedFiles.length) * 50);
					this.uploadProgress = progress;
					this.uploadStatus = `A preparar ficheiro ${index + 1} de ${this.selectedFiles.length}...`;
				});

				this.uploadStatus = 'A enviar ficheiros...';
				this.uploadProgress = 60;

				// Enviar para o backend
				const response = await this.postFormData('DOCUM_MultiUpload', 'FolderUploadCreate', formData);
				
				this.uploadProgress = 100;
				this.uploadStatus = 'Upload concluído!';
				
				this.uploadResult = response;
				
				if (response.success) {
					this.$emit('upload-complete', response);
				} else {
					this.$emit('upload-error', response);
				}

			} catch (error) {
				console.error('Erro no upload da pasta:', error);
				this.uploadResult = {
					success: false,
					message: 'Erro no upload: ' + error.message
				};
				this.$emit('upload-error', error);
			} finally {
				this.uploading = false;
			}
		},

		postFormData(controller, action, formData) {
			return new Promise((resolve, reject) => {
				postData(
					controller,
					action,
					formData,
					(data, response) => {
						console.log('Response from FolderUploadCreate:', { data, response });
						resolve(data);
					},
					(err) => {
						console.error('Error in FolderUploadCreate:', err);
						reject(err);
					}
				);
			});
		},

		clearSelection() {
			this.selectedFiles = [];
			this.folderStructure = [];
			this.uploadResult = null;
			this.baseFolderName = '';
			this.$refs.folderInput.value = '';
		}
	}
};
</script>

<style scoped>
.folder-upload-container {
	padding: 20px;
	border: 1px solid #ddd;
	border-radius: 8px;
	background-color: #f9f9f9;
}

.upload-section {
	margin-bottom: 20px;
	text-align: center;
}

.folder-select-btn {
	padding: 12px 24px;
	font-size: 16px;
	border-radius: 6px;
	border: none;
	background-color: #007bff;
	color: white;
	cursor: pointer;
	transition: background-color 0.2s;
}

.folder-select-btn:hover:not(:disabled) {
	background-color: #0056b3;
}

.folder-select-btn:disabled {
	background-color: #6c757d;
	cursor: not-allowed;
}

.file-count {
	margin-left: 15px;
	color: #666;
	font-style: italic;
}

.preview-section {
	margin-bottom: 20px;
}

.file-tree-preview {
	max-height: 300px;
	overflow-y: auto;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 10px;
	background-color: white;
	margin-bottom: 15px;
}

.folder-item {
	margin-bottom: 15px;
}

.folder-header {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 8px;
	background-color: #f8f9fa;
	border-radius: 4px;
	border-left: 3px solid #007bff;
}

.file-count-badge {
	background-color: #007bff;
	color: white;
	padding: 2px 8px;
	border-radius: 12px;
	font-size: 12px;
	margin-left: auto;
}

.folder-files {
	margin-left: 20px;
	margin-top: 8px;
}

.file-item {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 4px 0;
	color: #666;
}

.file-size {
	margin-left: auto;
	font-size: 12px;
	color: #999;
}

.more-files {
	color: #999;
	font-style: italic;
	padding: 4px 0;
}

.action-buttons {
	display: flex;
	gap: 10px;
	justify-content: center;
}

.btn {
	padding: 10px 20px;
	border: none;
	border-radius: 4px;
	cursor: pointer;
	font-size: 14px;
	transition: background-color 0.2s;
}

.btn-success {
	background-color: #28a745;
	color: white;
}

.btn-success:hover:not(:disabled) {
	background-color: #218838;
}

.btn-secondary {
	background-color: #6c757d;
	color: white;
}

.btn-secondary:hover:not(:disabled) {
	background-color: #545b62;
}

.btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.upload-progress {
	margin: 20px 0;
}

.progress {
	width: 100%;
	height: 20px;
	background-color: #e9ecef;
	border-radius: 10px;
	overflow: hidden;
}

.progress-bar {
	height: 100%;
	background-color: #007bff;
	transition: width 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 12px;
}

.upload-status {
	text-align: center;
	margin-top: 10px;
	color: #666;
}

.upload-result {
	margin-top: 20px;
}

.alert {
	padding: 15px;
	border-radius: 4px;
	margin-bottom: 20px;
}

.alert-success {
	background-color: #d4edda;
	border: 1px solid #c3e6cb;
	color: #155724;
}

.alert-danger {
	background-color: #f8d7da;
	border: 1px solid #f5c6cb;
	color: #721c24;
}

.upload-errors {
	margin-top: 10px;
}

.upload-errors ul {
	margin: 5px 0 0 20px;
}
</style>
