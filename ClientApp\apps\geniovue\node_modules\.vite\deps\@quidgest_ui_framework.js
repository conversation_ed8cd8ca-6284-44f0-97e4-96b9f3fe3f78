import {
  E2 as E,
  g2 as g,
  i,
  n2 as n
} from "./chunk-UT6WI4VU.js";
import "./chunk-GUQP5DHG.js";
import {
  ref,
  watch
} from "./chunk-6UQYPIB4.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/templates/theme.js
var f = {
  primary: "#008ad2",
  primaryLight: "#cde5ff",
  primaryDark: "#006398",
  secondary: "#001d31",
  secondaryLight: "#e6eef3",
  secondaryDark: "#001425",
  highlight: "#ff8241",
  highlightLight: "#ffe4d6",
  highlightDark: "#cc4700",
  info: "#17a2b8",
  infoLight: "#bceff7",
  infoDark: "#11798a",
  success: "#28a745",
  successLight: "#c2f0cd",
  successDark: "#1e7d34",
  warning: "#ffa900",
  warningLight: "#ffeabf",
  warningDark: "#bf7f00",
  danger: "#b71c1c",
  dangerLight: "#f7cccc",
  dangerDark: "#891515",
  background: "#fff",
  container: "#fff",
  neutral: "#7c858d",
  neutralLight: "#c4c5ca",
  neutralDark: "#40474f",
  onBackground: "#202428",
  onPrimary: "#fff",
  onSecondary: "#fff",
  onHighlight: "#fff",
  onSuccess: "#fff",
  onWarning: "#fff",
  onDanger: "#fff",
  onInfo: "#fff",
  onNeutral: "#fff"
};
var a = {
  primary: "#008ad2",
  primaryLight: "#006398",
  primaryDark: "#cde5ff",
  secondary: "#003552",
  secondaryLight: "#001425",
  secondaryDark: "#e6eef3",
  highlight: "#ff8241",
  highlightLight: "#cc4700",
  highlightDark: "#ffe4d6",
  info: "#17a2b8",
  infoLight: "#11798a",
  infoDark: "#bceff7",
  success: "#28a745",
  successLight: "#1e7d34",
  successDark: "#c2f0cd",
  warning: "#ffa900",
  warningLight: "#bf7f00",
  warningDark: "#ffeabf",
  danger: "#b71c1c",
  dangerLight: "#891515",
  dangerDark: "#f7cccc",
  background: "#202428",
  container: "#202428",
  neutral: "#9299a0",
  neutralLight: "#40474f",
  neutralDark: "#c4c5ca",
  onBackground: "#fff",
  onPrimary: "#fff",
  onSecondary: "#fff",
  onHighlight: "#fff",
  onSuccess: "#fff",
  onWarning: "#fff",
  onDanger: "#fff",
  onInfo: "#fff",
  onNeutral: "#fff"
};

// ../../node_modules/.pnpm/@quidgest+ui@0.16.3_axios@1.7.9_sortablejs@1.15.6_vue@3.5.13_typescript@5.8.3_/node_modules/@quidgest/ui/esm/framework.js
function D(s = {}) {
  return { install: (o) => {
    const t = s.components || {};
    for (const n2 in t)
      o.component(n2, t[n2]);
    const e = s.defaults || {};
    o.provide(i, ref(e)), p(o, s.themes);
  } };
}
function p(s, m) {
  const o = [];
  let t;
  if (!m)
    t = "default", o.push({
      name: t,
      mode: "light",
      scheme: f
    });
  else
    for (const e of m.themes) {
      const r = { ...e.mode === "light" ? f : a, ...e.colors };
      o.push({
        name: e.name,
        mode: e.mode,
        scheme: r
      }), e.name === m.defaultTheme && (t = e.name);
    }
  if (t) {
    const e = ref(t), n2 = E(e, o);
    g(o), watch(n2.name, S, { immediate: true }), s.provide(n, n2);
  }
}
function S(s) {
  const m = document.documentElement, t = Array.from(m.classList).filter((e) => !e.startsWith("q-theme"));
  m.className = t.join(" "), m.classList.add(`q-theme--${s}`);
}
export {
  D as createFramework
};
//# sourceMappingURL=@quidgest_ui_framework.js.map
