import {
  getTag_default
} from "./chunk-7UR4B3QI.js";
import "./chunk-7DFSKHLR.js";
import "./chunk-KUIRPFKY.js";
import "./chunk-P5WJJE5X.js";
import {
  baseKeys_default
} from "./chunk-6AKQ6PKI.js";
import "./chunk-Q2I7EFQJ.js";
import {
  isBuffer_default,
  isPrototype_default,
  isTypedArray_default
} from "./chunk-BPIZ5UIH.js";
import {
  isArrayLike_default
} from "./chunk-JFUT5HMH.js";
import {
  isArguments_default
} from "./chunk-KBVNP3C6.js";
import "./chunk-KKDVC4X3.js";
import "./chunk-ZNZP756G.js";
import "./chunk-X3F52GTU.js";
import {
  isArray_default
} from "./chunk-VO4BPRKV.js";
import "./chunk-SNQ64GCV.js";
import "./chunk-VB7E2QJD.js";
import "./chunk-ZJQW7BA7.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isEmpty.js
var mapTag = "[object Map]";
var setTag = "[object Set]";
var objectProto = Object.prototype;
var hasOwnProperty = objectProto.hasOwnProperty;
function isEmpty(value) {
  if (value == null) {
    return true;
  }
  if (isArrayLike_default(value) && (isArray_default(value) || typeof value == "string" || typeof value.splice == "function" || isBuffer_default(value) || isTypedArray_default(value) || isArguments_default(value))) {
    return !value.length;
  }
  var tag = getTag_default(value);
  if (tag == mapTag || tag == setTag) {
    return !value.size;
  }
  if (isPrototype_default(value)) {
    return !baseKeys_default(value).length;
  }
  for (var key in value) {
    if (hasOwnProperty.call(value, key)) {
      return false;
    }
  }
  return true;
}
var isEmpty_default = isEmpty;
export {
  isEmpty_default as default
};
//# sourceMappingURL=lodash-es_isEmpty.js.map
