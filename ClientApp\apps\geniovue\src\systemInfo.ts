﻿// @ts-expect-error genericFunctions does not export type definitions yet
import { getLayoutVariables } from '@quidgest/clientapp/utils/genericFunctions'

import layoutConfigJson from './assets/config/Layoutconfig.json'

export const systemInfo = {
	applicationName: 'Interaktivt Elektronisk Teknisk Dokumentations System',

	genio: {
		buildVersion: 613,
		dbIdxVersion: 98,
		dbVersion: '2569',
		genioVersion: '370,07',
		trackChangesVersion: '0',
		assemblyVersion: '370,07.2569.0.613',
		generationDate: {
			year: 2025,
			month: 6,
			day: 20
		}
	},

	system: {
		acronym: 'FMI',
		name: 'Forsvarsministeriets',
		baseCurrency: {
			symbol: '€',
			code: 'EUR',
			precision: 2
		}
	},

	locale: {
		defaultLocale: 'en-US',
		availableLocales: [
			{
				language: 'en-US',
				acronym: 'EN',
				displayName: 'English'
			},
		]
	},

	// FIXME: This should be the generator's responsibility, not the client app.
	layout: getLayoutVariables(layoutConfigJson),

	authConfig: {
		useCertificate: false,
		maxUsrSize: 100,
		maxPswSize: 150
	},

	cookies: {
		cookieText: '',
		cookieActive: false,
		filePath: ''
	},

	isCavAvailable: false,

	isChatBotAvailable: false,

	isSuggestionsAvailable: false,

	appAlerts: [
	],

	userRegistration: {
		allowRegistration: false,
		registrationTypes: [
		]
	},

	resourcesPath: 'Content/img/'
}
