﻿

using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.SqlTypes;
using System.Text;
using CSGenio.framework;
using CSGenio.persistence;
using Quidgest.Persistence;
using Quidgest.Persistence.GenericQuery;
using System.Linq;

namespace CSGenio.business
{
	/// <summary>
	/// Document
	/// </summary>
	public class CSGenioAdocum : DbArea
	{
		/// <summary>
		/// Meta-information on this area
		/// </summary>
		protected readonly static AreaInfo informacao = InicializaAreaInfo();

		public CSGenioAdocum(User user, string module)
		{
            this.user = user;
            this.module = module;
			// USE /[MANUAL TDS CONSTRUTOR DOCUM]/
		}

		public CSGenioAdocum(User user) : this(user, user.CurrentModule)
		{
		}

		/// <summary>
		/// Initializes the metadata relative to the fields of this area
		/// </summary>
		private static void InicializaCampos(AreaInfo info)
		{
			Field Qfield = null;
#pragma warning disable CS0168, S1481 // Variable is declared but never used
			List<ByAreaArguments> argumentsListByArea;
#pragma warning restore CS0168, S1481 // Variable is declared but never used
			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "coddocum", FieldType.KEY_GUID);
			Qfield.FieldDescription = "";
			Qfield.FieldSize =  36;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "coddscrp", FieldType.KEY_GUID);
			Qfield.FieldDescription = "FKey Descriptive parent";
			Qfield.FieldSize =  36;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "FKEY_DESCRIPTIVE_PAR65256";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "docum", FieldType.DOCUMENT);
			Qfield.FieldDescription = "Document";
			Qfield.FieldSize =  255;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "DOCUMENT00695";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);
 			Qfield = new Field(info.Alias, "documfk", FieldType.KEY_GUID);
			Qfield.FieldSize = 16;
			Qfield.FieldDescription = "Chave estrangeira para o documento";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "relpath", FieldType.TEXT);
			Qfield.FieldDescription = "Path relativo";
			Qfield.FieldSize =  260;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "PATH_RELATIVO65224";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "islink", FieldType.LOGIC);
			Qfield.FieldDescription = "Save link to file";
			Qfield.FieldSize =  1;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "SAVE_LINK_TO_FILE62068";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "linkfile", FieldType.TEXT);
			Qfield.FieldDescription = "Link to file";
			Qfield.FieldSize =  255;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "LINK_TO_FILE35721";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "docname", FieldType.TEXT);
			Qfield.FieldDescription = "File name";
			Qfield.FieldSize =  255;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "FILE_NAME37493";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "isfolder", FieldType.LOGIC);
			Qfield.FieldDescription = "Is folder?";
			Qfield.FieldSize =  1;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "IS_FOLDER_40428";

			Qfield.Dupmsg = "";
			Qfield.DefaultValue = new DefaultValue(0);
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "cre_date", FieldType.DATETIMESECONDS);
			Qfield.FieldDescription = "Created by";
			Qfield.FieldSize =  8;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "CREATED_BY12292";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "cre_user", FieldType.TEXT);
			Qfield.FieldDescription = "Created by";
			Qfield.FieldSize =  100;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "CREATED_BY12292";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "upd_date", FieldType.DATETIMESECONDS);
			Qfield.FieldDescription = "Amended in";
			Qfield.FieldSize =  8;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "AMENDED_IN20558";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "upd_user", FieldType.TEXT);
			Qfield.FieldDescription = "Amended by";
			Qfield.FieldSize =  100;
			Qfield.MQueue = false;
			Qfield.CavDesignation = "AMENDED_BY63350";

			Qfield.Dupmsg = "";
			info.RegisterFieldDB(Qfield);

			//- - - - - - - - - - - - - - - - - - -
			Qfield = new Field(info.Alias, "zzstate", FieldType.INTEGER);
			Qfield.FieldDescription = "Estado da ficha";
			info.RegisterFieldDB(Qfield);

		}

		/// <summary>
		/// Initializes metadata for paths direct to other areas
		/// </summary>
		private static void InicializaRelacoes(AreaInfo info)
		{
			// Daughters Relations
			//------------------------------

			// Mother Relations
			//------------------------------
			info.ParentTables = new Dictionary<string, Relation>();
			info.ParentTables.Add("dscpp", new Relation("TDS", "tdsdocum", "docum", "coddocum", "coddscrp", "TDS", "tdsdescriptive", "dscpp", "coddscrp", "coddscrp"));
		}

		/// <summary>
		/// Initializes metadata for indirect paths to other areas
		/// </summary>
		private static void InicializaCaminhos(AreaInfo info)
		{
			// Pathways
			//------------------------------
			info.Pathways = new Dictionary<string, string>(7);
			info.Pathways.Add("dscpp","dscpp");
			info.Pathways.Add("langu","dscpp");
			info.Pathways.Add("count","dscpp");
			info.Pathways.Add("compo","dscpp");
			info.Pathways.Add("compr","dscpp");
			info.Pathways.Add("dmcdr","dscpp");
			info.Pathways.Add("dmcdd","dscpp");
		}

		/// <summary>
		/// Initializes metadata for triggers and formula arguments
		/// </summary>
		private static void InicializaFormulas(AreaInfo info)
		{
			// Formulas
			//------------------------------



			info.DefaultValues = new string[] {
			 "isfolder"
			};






			//Write conditions
			List<ConditionFormula> conditions = new List<ConditionFormula>();
			info.WriteConditions = conditions.Where(c=> c.IsWriteCondition()).ToList();
			info.CrudConditions = conditions.Where(c=> c.IsCrudCondition()).ToList();

		}

		/// <summary>
		/// static CSGenioAdocum()
		/// </summary>
		private static AreaInfo InicializaAreaInfo()
		{
			AreaInfo info = new AreaInfo();

			// Area meta-information
			info.QSystem="TDS";
			info.TableName="tdsdocum";
			info.ShadowTabName="";
			info.ShadowTabKeyName="";

			info.PrimaryKeyName="coddocum";
			info.HumanKeyName="docname,".TrimEnd(',');
			info.Alias="docum";
			info.IsDomain = true;
			info.PersistenceType = PersistenceType.Database;
			info.AreaDesignation="Document";
			info.AreaPluralDesignation="Documents";
			info.DescriptionCav="DOCUMENT00695";

			//sincronização
			info.SyncIncrementalDateStart = TimeSpan.FromHours(8);
			info.SyncIncrementalDateEnd = TimeSpan.FromHours(23);
			info.SyncCompleteHour = TimeSpan.FromHours(0.5);
			info.SyncIncrementalPeriod = TimeSpan.FromHours(1);
			info.BatchSync = 100;
			info.SyncType = SyncType.Central;
            info.SolrList = new List<string>();
        	info.QueuesList = new List<GenioServer.business.QueueGenio>();





			//RS 22.03.2011 I separated in submetodos due to performance problems with the JIT in 64bits
			// that in very large projects took 2 minutes on the first call.
			// After a Microsoft analysis of the JIT algortimo it was revealed that it has a
			// complexity O(n*m) where n are the lines of code and m the number of variables of a function.
			// Tests have revealed that splitting into subfunctions cuts the JIT time by more than half by 64-bit.
			//------------------------------
			InicializaCampos(info);

			//------------------------------
			InicializaRelacoes(info);

			//------------------------------
			InicializaCaminhos(info);

			//------------------------------
			InicializaFormulas(info);

			// Automatic audit stamps in BD
            //------------------------------
			info.StampFieldsIns = new string[] {
                "cre_user","cre_date"
			};

			info.StampFieldsAlt = new string[] {
                "upd_user","upd_date"
			};
            // Documents in DB
            //------------------------------
			info.DocumsForeignKeys = new List<String> {
			 "documfk"
			};
			info.HasVersionManagment = true; //a true por omissão, quando o Qfield no genio tiver criado preencher por esse Qvalue

            // Historics
            //------------------------------

			// Duplication
			//------------------------------

			// Ephs
			//------------------------------
			info.Ephs=new Hashtable();

			// Table minimum roles and access levels
			//------------------------------
            info.QLevel = new QLevel();
            info.QLevel.Query = Role.AUTHORIZED;
            info.QLevel.Create = Role.AUTHORIZED;
            info.QLevel.AlterAlways = Role.AUTHORIZED;
            info.QLevel.RemoveAlways = Role.AUTHORIZED;

      		return info;
		}

		/// <summary>
		/// Meta-information about this area
		/// </summary>
		public override AreaInfo Information
		{
			get { return informacao; }
		}
		/// <summary>
		/// Meta-information about this area
		/// </summary>
		public static AreaInfo GetInformation()
		{
			return informacao;
		}

		/// <summary>Field : "" Tipo: "+" Formula:  ""</summary>
		public static FieldRef FldCoddocum { get { return m_fldCoddocum; } }
		private static FieldRef m_fldCoddocum = new FieldRef("docum", "coddocum");

		/// <summary>Field : "" Tipo: "+" Formula:  ""</summary>
		public string ValCoddocum
		{
			get { return (string)returnValueField(FldCoddocum); }
			set { insertNameValueField(FldCoddocum, value); }
		}

		/// <summary>Field : "FKey Descriptive parent" Tipo: "CE" Formula:  ""</summary>
		public static FieldRef FldCoddscrp { get { return m_fldCoddscrp; } }
		private static FieldRef m_fldCoddscrp = new FieldRef("docum", "coddscrp");

		/// <summary>Field : "FKey Descriptive parent" Tipo: "CE" Formula:  ""</summary>
		public string ValCoddscrp
		{
			get { return (string)returnValueField(FldCoddscrp); }
			set { insertNameValueField(FldCoddscrp, value); }
		}

		/// <summary>Field : "Document" Tipo: "IB" Formula:  ""</summary>
		public static FieldRef FldDocum { get { return m_fldDocum; } }
		private static FieldRef m_fldDocum = new FieldRef("docum", "docum");

		/// <summary>Field : "Document" Tipo: "IB" Formula:  ""</summary>
		public string ValDocum
		{
			get { return (string)returnValueField(FldDocum); }
			set { insertNameValueField(FldDocum, value); }
		}

		/// <summary>Field : "Document FK" Tipo: "CE" Formula:  ""</summary>
		public static FieldRef FldDocumfk { get { return m_fldDocumfk; } }
		private static FieldRef m_fldDocumfk = new FieldRef("docum", "documfk");

		/// <summary>Field : "Document FK" Tipo: "CE" Formula:  ""</summary>
		public string ValDocumfk
		{
			get { return (string)returnValueField(FldDocumfk); }
			set { insertNameValueField(FldDocumfk, value); }
		}

		/// <summary>Field : "Path relativo" Tipo: "C" Formula:  ""</summary>
		public static FieldRef FldRelpath { get { return m_fldRelpath; } }
		private static FieldRef m_fldRelpath = new FieldRef("docum", "relpath");

		/// <summary>Field : "Path relativo" Tipo: "C" Formula:  ""</summary>
		public string ValRelpath
		{
			get { return (string)returnValueField(FldRelpath); }
			set { insertNameValueField(FldRelpath, value); }
		}

		/// <summary>Field : "Save link to file" Tipo: "L" Formula:  ""</summary>
		public static FieldRef FldIslink { get { return m_fldIslink; } }
		private static FieldRef m_fldIslink = new FieldRef("docum", "islink");

		/// <summary>Field : "Save link to file" Tipo: "L" Formula:  ""</summary>
		public int ValIslink
		{
			get { return (int)returnValueField(FldIslink); }
			set { insertNameValueField(FldIslink, value); }
		}

		/// <summary>Field : "Link to file" Tipo: "C" Formula:  ""</summary>
		public static FieldRef FldLinkfile { get { return m_fldLinkfile; } }
		private static FieldRef m_fldLinkfile = new FieldRef("docum", "linkfile");

		/// <summary>Field : "Link to file" Tipo: "C" Formula:  ""</summary>
		public string ValLinkfile
		{
			get { return (string)returnValueField(FldLinkfile); }
			set { insertNameValueField(FldLinkfile, value); }
		}

		/// <summary>Field : "File name" Tipo: "C" Formula:  ""</summary>
		public static FieldRef FldDocname { get { return m_fldDocname; } }
		private static FieldRef m_fldDocname = new FieldRef("docum", "docname");

		/// <summary>Field : "File name" Tipo: "C" Formula:  ""</summary>
		public string ValDocname
		{
			get { return (string)returnValueField(FldDocname); }
			set { insertNameValueField(FldDocname, value); }
		}

		/// <summary>Field : "Is folder?" Tipo: "L" Formula:  ""</summary>
		public static FieldRef FldIsfolder { get { return m_fldIsfolder; } }
		private static FieldRef m_fldIsfolder = new FieldRef("docum", "isfolder");

		/// <summary>Field : "Is folder?" Tipo: "L" Formula:  ""</summary>
		public int ValIsfolder
		{
			get { return (int)returnValueField(FldIsfolder); }
			set { insertNameValueField(FldIsfolder, value); }
		}

		/// <summary>Field : "Created by" Tipo: "OD" Formula:  ""</summary>
		public static FieldRef FldCre_date { get { return m_fldCre_date; } }
		private static FieldRef m_fldCre_date = new FieldRef("docum", "cre_date");

		/// <summary>Field : "Created by" Tipo: "OD" Formula:  ""</summary>
		public DateTime ValCre_date
		{
			get { return (DateTime)returnValueField(FldCre_date); }
			set { insertNameValueField(FldCre_date, value); }
		}

		/// <summary>Field : "Created by" Tipo: "ON" Formula:  ""</summary>
		public static FieldRef FldCre_user { get { return m_fldCre_user; } }
		private static FieldRef m_fldCre_user = new FieldRef("docum", "cre_user");

		/// <summary>Field : "Created by" Tipo: "ON" Formula:  ""</summary>
		public string ValCre_user
		{
			get { return (string)returnValueField(FldCre_user); }
			set { insertNameValueField(FldCre_user, value); }
		}

		/// <summary>Field : "Amended in" Tipo: "ED" Formula:  ""</summary>
		public static FieldRef FldUpd_date { get { return m_fldUpd_date; } }
		private static FieldRef m_fldUpd_date = new FieldRef("docum", "upd_date");

		/// <summary>Field : "Amended in" Tipo: "ED" Formula:  ""</summary>
		public DateTime ValUpd_date
		{
			get { return (DateTime)returnValueField(FldUpd_date); }
			set { insertNameValueField(FldUpd_date, value); }
		}

		/// <summary>Field : "Amended by" Tipo: "EN" Formula:  ""</summary>
		public static FieldRef FldUpd_user { get { return m_fldUpd_user; } }
		private static FieldRef m_fldUpd_user = new FieldRef("docum", "upd_user");

		/// <summary>Field : "Amended by" Tipo: "EN" Formula:  ""</summary>
		public string ValUpd_user
		{
			get { return (string)returnValueField(FldUpd_user); }
			set { insertNameValueField(FldUpd_user, value); }
		}

		/// <summary>Field : "ZZSTATE" Type: "INT" Formula:  ""</summary>
		public static FieldRef FldZzstate { get { return m_fldZzstate; } }
		private static FieldRef m_fldZzstate = new FieldRef("docum", "zzstate");



		/// <summary>Field : "ZZSTATE" Type: "INT"</summary>
		public int ValZzstate
		{
			get { return (int)returnValueField(FldZzstate); }
			set { insertNameValueField(FldZzstate, value); }
		}

        /// <summary>
        /// Obtains a partially populated area with the record corresponding to a primary key
        /// </summary>
        /// <param name="sp">Persistent support from where to get the registration</param>
        /// <param name="key">The value of the primary key</param>
        /// <param name="user">The context of the user</param>
        /// <param name="fields">The fields to be filled in the area</param>
        /// <returns>An area with the fields requests of the record read or null if the key does not exist</returns>
        /// <remarks>Persistence operations should not be used on a partially positioned register</remarks>
        public static CSGenioAdocum search(PersistentSupport sp, string key, User user, string[] fields = null)
        {
			if (string.IsNullOrEmpty(key))
				return null;

		    CSGenioAdocum area = new CSGenioAdocum(user, user.CurrentModule);

            if (sp.getRecord(area, key, fields))
                return area;
			return null;
        }


		public static string GetkeyFromControlledRecord(PersistentSupport sp, string ID, User user)
		{
			if (informacao.ControlledRecords != null)
				return informacao.ControlledRecords.GetPrimaryKeyFromControlledRecord(sp, user, ID);
			return String.Empty;
		}


        /// <summary>
        /// Search for all records of this area that comply with a condition
        /// </summary>
        /// <param name="sp">Persistent support from where to get the list</param>
        /// <param name="user">The context of the user</param>
        /// <param name="where">The search condition for the records. Use null to get all records</param>
        /// <param name="fields">The fields to be filled in the area</param>
        /// <param name="distinct">Get distinct from fields</param>
        /// <param name="noLock">NOLOCK</param>
        /// <returns>A list of area records with all fields populated</returns>
        /// <remarks>Persistence operations should not be used on a partially positioned register</remarks>
        public static List<CSGenioAdocum> searchList(PersistentSupport sp, User user, CriteriaSet where, string[] fields = null, bool distinct = false, bool noLock = false)
        {
				return sp.searchListWhere<CSGenioAdocum>(where, user, fields, distinct, noLock);
        }



       	/// <summary>
        /// Search for all records of this area that comply with a condition
        /// </summary>
        /// <param name="sp">Persistent support from where to get the list</param>
        /// <param name="user">The context of the user</param>
        /// <param name="where">The search condition for the records. Use null to get all records</param>
        /// <param name="listing">List configuration</param>
        /// <returns>A list of area records with all fields populated</returns>
        /// <remarks>Persistence operations should not be used on a partially positioned register</remarks>
        public static void searchListAdvancedWhere(PersistentSupport sp, User user, CriteriaSet where, ListingMVC<CSGenioAdocum> listing)
        {
			sp.searchListAdvancedWhere<CSGenioAdocum>(where, listing);
        }




		/// <summary>
		/// Check if a record exist
		/// </summary>
		/// <param name="key">Record key</param>
		/// <param name="sp">DB conecntion</param>
		/// <returns>True if the record exist</returns>
		public static bool RecordExist(string key, PersistentSupport sp) => DbArea.RecordExist(key, informacao, sp);




 


		// USE /[MANUAL TDS TABAUX DOCUM]/

 

             

	}
}
