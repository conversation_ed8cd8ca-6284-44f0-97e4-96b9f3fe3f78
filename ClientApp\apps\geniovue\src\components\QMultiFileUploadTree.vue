<template>
	<div class="multi-file-upload-tree">
		<!-- <PERSON><PERSON> de ferramentas -->
		<div class="toolbar">
			<div class="toolbar-left">
				<!-- Input oculto para seleção de pasta -->
				<input
					ref="folderInput"
					type="file"
					webkitdirectory
					directory
					multiple
					@change="handleFolderSelect"
					style="display: none;"
					accept="*/*"
				/>

				<button
					@click="selectFolder"
					:disabled="uploading"
					class="btn btn-primary">
					<i class="fas fa-folder-plus"></i>
					{{ uploading ? 'Uploading...' : 'Upload Folder' }}
				</button>

				<button
					@click="refreshTree"
					:disabled="loading"
					class="btn btn-secondary">
					<i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
					Refresh
				</button>
			</div>

			<div class="toolbar-right">
				<div class="info-text">
					{{ totalFolders }} folders, {{ totalDocuments }} files
				</div>
			</div>
		</div>

		<!-- Se<PERSON> de upload (quando há ficheiros selecionados) -->
		<div v-if="selectedFiles.length > 0" class="upload-section">
			<div class="upload-preview">
				<h5>
					<i class="fas fa-upload"></i>
					Files to upload: {{ selectedFiles.length }}
				</h5>
				<div class="folder-preview">
					<strong>{{ baseFolderName }}</strong>
					<span class="file-count-badge">{{ selectedFiles.length }} file(s)</span>
				</div>

				<div class="upload-actions">
					<button
						@click="uploadFolder"
						:disabled="uploading"
						class="btn btn-success">
						<i class="fas fa-upload"></i>
						{{ uploading ? 'Uploading...' : 'Upload Folder' }}
					</button>

					<button
						@click="clearSelection"
						:disabled="uploading"
						class="btn btn-outline-secondary">
						<i class="fas fa-times"></i>
						Cancel
					</button>
				</div>
			</div>

			<!-- Indicador de upload -->
			<div v-if="uploading" class="upload-progress">
				<div class="upload-header">
					<i class="fas fa-spinner fa-spin"></i>
					{{ uploadStatus }}
				</div>
				<div class="progress">
					<div
						class="progress-bar"
						:style="{ width: uploadProgress + '%' }"
						role="progressbar">
						{{ uploadProgress }}%
					</div>
				</div>
			</div>
		</div>

		<!-- Mensagens de resultado -->
		<div v-if="uploadResult" class="upload-result">
			<div
				:class="['alert', uploadResult.success ? 'alert-success' : 'alert-danger']"
				@click="clearUploadResult">
				<i :class="uploadResult.success ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
				{{ uploadResult.message }}
				<button class="close-btn">&times;</button>
			</div>
		</div>

		<!-- Mensagem se não há dados -->
		<div v-if="!treeData || treeData.length === 0"
			 class="empty-state">
			<i class="fas fa-folder-open"></i>
			<p>No documents found</p>
			<p>Use "Upload Folder" to add files with folder structure</p>
		</div>

		<!-- Renderização da árvore -->
		<div v-else class="tree-container">
			<!-- Cabeçalho da tabela -->
			<div class="table-header">
				<div>Name</div>
				<div style="text-align: center;">Type</div>
				<div style="text-align: right;">Size</div>
				<div style="text-align: center;">Modified</div>
			</div>

			<!-- Itens da árvore -->
			<template v-for="item in treeData" :key="item.id">
				<QTreeItem
					:item="item"
					:level="0"
					@toggle-folder="toggleFolder"
					@move-item="handleMoveItem" />
			</template>
		</div>
	</div>
</template>

<script>
import { fetchDocumentTree, moveDocuments, buildTreeFromApiData, countDocuments, countFolders, formatFileSize } from './documMultiUploadTree.js'
import { gravarTodosOsDocum, extractRelativePath, criarDocumComFicheiro } from './documMultiUpload.js'
import QTreeItem from './QTreeItem.vue'

export default {
	name: 'QMultiFileUploadTree',

	props: {
		parentId: {
			type: String,
			required: true
		},
		parentTable: {
			type: String,
			default: 'DSCPP'
		}
	},

	emits: ['move-success', 'move-error', 'upload-complete', 'upload-error'],

	data() {
		return {
			treeData: [],
			loading: false,

			// Drag and Drop
			draggedItem: null,
			dropTargetItem: null,

			// Upload de pasta
			selectedFiles: [],
			baseFolderName: '',
			uploading: false,
			uploadProgress: 0,
			uploadStatus: 'Uploading files...',
			uploadResult: null
		};
	},

	computed: {
		/**
		 * Conta o total de documentos (ficheiros) na árvore recursivamente
		 */
		totalDocuments() {
			return countDocuments(this.treeData || []);
		},

		/**
		 * Conta o total de pastas na árvore recursivamente
		 */
		totalFolders() {
			return countFolders(this.treeData || []);
		}
	},

	components: {
		QTreeItem
	},

	methods: {
		/**
		 * Carrega os dados da árvore
		 */
		async loadTreeData() {
			if (!this.parentId) {
				console.warn('parentId não fornecido para carregar árvore');
				return;
			}

			this.loading = true;
			try {
				console.log('Carregando árvore para parentId:', this.parentId);
				const apiData = await fetchDocumentTree(this.parentId);
				this.treeData = buildTreeFromApiData(apiData);
				console.log('Árvore carregada com sucesso:', this.treeData.length, 'itens na raiz');
			} catch (error) {
				console.error('Erro ao carregar árvore:', error);
				this.treeData = [];
			} finally {
				this.loading = false;
			}
		},

		/**
		 * Atualiza a árvore
		 */
		async refreshTree() {
			await this.loadTreeData();
		},

		/**
		 * Abre o seletor de pasta
		 */
		selectFolder() {
			this.$refs.folderInput.click();
		},

		/**
		 * Manipula a seleção de pasta
		 */
		handleFolderSelect(event) {
			const files = Array.from(event.target.files);
			if (files.length === 0) return;

			this.selectedFiles = files;
			this.uploadResult = null;

			// Extrair o nome da pasta base
			this.extractBaseFolderName(files);

			console.log(`Pasta selecionada: ${this.baseFolderName} com ${files.length} ficheiros`);
		},

		/**
		 * Extrai o nome da pasta base
		 */
		extractBaseFolderName(files) {
			if (files.length === 0) return;

			const firstFile = files[0];
			const pathParts = firstFile.webkitRelativePath.split('/');
			this.baseFolderName = pathParts[0];

			console.log('Pasta base extraída:', this.baseFolderName);
		},

		/**
		 * Faz upload da pasta selecionada usando a mesma lógica do QMultiFileUploadPanel
		 */
		async uploadFolder() {
			if (this.selectedFiles.length === 0) return;

			this.uploading = true;
			this.uploadProgress = 0;
			this.uploadStatus = 'Preparing files...';
			this.uploadResult = null;

			try {
				// Preparar lista de documentos com relPath
				const documListWithParent = this.selectedFiles.map(file => {
					const relPath = extractRelativePath(file.webkitRelativePath, this.baseFolderName);
					return {
						file: file,
						nome: file.name,
						descricao: '', // Sem descrição para upload de pasta
						parentId: this.parentId,
						parentTable: this.parentTable,
						relPath: relPath
					};
				});

				console.log('Uploading folder with', documListWithParent.length, 'files');

				this.uploadStatus = 'Uploading files...';
				this.uploadProgress = 5;

				// Usar a mesma função que o QMultiFileUploadPanel com progresso
				const results = await this.gravarTodosOsDocumComProgresso(documListWithParent);

				// Finalizar progresso
				this.uploadStatus = 'Finalizing...';
				this.uploadProgress = 95;

				// Processar resultados
				const successCount = results.filter(r => r.success !== false).length;
				const errorCount = results.length - successCount;

				this.uploadResult = {
					success: errorCount === 0,
					message: `${successCount} file(s) uploaded successfully` + (errorCount > 0 ? `, ${errorCount} error(s)` : ''),
					uploadedFiles: results.filter(r => r.success !== false),
					errors: results.filter(r => r.success === false).map(r => r.error),
					totalFiles: results.length,
					successCount: successCount,
					errorCount: errorCount
				};

				if (this.uploadResult.success || successCount > 0) {
					// Recarregar a árvore
					this.uploadStatus = 'Refreshing tree...';
					this.uploadProgress = 98;
					await this.loadTreeData();

					// Finalizar
					this.uploadProgress = 100;
					this.uploadStatus = 'Upload completed!';

					// Limpar seleção
					this.clearSelection();

					this.$emit('upload-complete', this.uploadResult);
				} else {
					this.$emit('upload-error', this.uploadResult);
				}

			} catch (error) {
				console.error('Erro no upload da pasta:', error);
				this.uploadResult = {
					success: false,
					message: 'Upload error: ' + error.message
				};
				this.$emit('upload-error', error);
			} finally {
				this.uploading = false;
			}
		},



		/**
		 * Limpa a seleção de ficheiros
		 */
		clearSelection() {
			this.selectedFiles = [];
			this.baseFolderName = '';
			this.uploadResult = null;
			this.$refs.folderInput.value = '';
		},

		/**
		 * Limpa o resultado do upload
		 */
		clearUploadResult() {
			this.uploadResult = null;
		},

		/**
		 * Versão de gravarTodosOsDocum com atualização de progresso
		 */
		async gravarTodosOsDocumComProgresso(documList) {
			const results = [];
			const total = documList.length;

			for (let i = 0; i < documList.length; i++) {
				const { file, nome, descricao, parentId, parentTable, relPath } = documList[i];

				// Atualizar status
				this.uploadStatus = `Uploading file ${i + 1} of ${total}: ${nome}`;

				try {
					const result = await criarDocumComFicheiro(file, { nome, descricao, parentId, parentTable, relPath });
					results.push({ fileName: file.name, ...result });
				} catch (err) {
					results.push({ fileName: file.name, success: false, error: err.message });
				}

				// Atualizar progresso (10% a 90%, deixando 10% para finalização)
				const progress = Math.round(10 + ((i + 1) / total) * 80);
				this.uploadProgress = progress;
			}

			return results;
		},

		/**
		 * Alterna a expansão de uma pasta
		 */
		toggleFolder(item) {
			if (item.isFolder) {
				item.expanded = !item.expanded;
			}
		},

		/**
		 * Manipula o movimento de itens
		 */
		async handleMoveItem(moveData) {
			const { draggedItem, targetFolder, newRelPath } = moveData;

			console.log('Movendo item:', {
				from: draggedItem.name,
				to: targetFolder.name,
				newPath: newRelPath
			});

			try {
				const response = await moveDocuments([draggedItem.id], newRelPath);

				if (response && response.success) {
					// Recarregar a árvore
					await this.loadTreeData();

					this.$emit('move-success', {
						item: draggedItem.name,
						target: targetFolder.name,
						message: response.message
					});
				} else {
					throw new Error(response?.message || 'Erro desconhecido');
				}
			} catch (error) {
				console.error('Erro ao mover documento:', error);
				this.$emit('move-error', {
					item: draggedItem.name,
					target: targetFolder.name,
					error: error.message
				});
			}
		}
	},

	mounted() {
		// Carregar dados quando o componente é montado
		if (this.parentId) {
			this.loadTreeData();
		}
	},

	watch: {
		parentId: {
			handler(newParentId) {
				if (newParentId) {
					this.loadTreeData();
				}
			}
		}
	}
};
</script>

<style scoped>
.multi-file-upload-tree {
	width: 100%;
	border: 1px solid #dee2e6;
	border-radius: 8px;
	overflow: hidden;
	background-color: white;
}

/* Barra de ferramentas */
.toolbar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	background-color: #f8f9fa;
	border-bottom: 1px solid #dee2e6;
}

.toolbar-left {
	display: flex;
	gap: 10px;
}

.toolbar-right {
	display: flex;
	align-items: center;
}

.info-text {
	font-size: 14px;
	color: #6c757d;
	font-style: italic;
}

.btn {
	padding: 8px 16px;
	border: none;
	border-radius: 6px;
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
	transition: all 0.2s ease;
	display: flex;
	align-items: center;
	gap: 6px;
}

.btn-primary {
	background-color: #007bff;
	color: white;
}

.btn-primary:hover:not(:disabled) {
	background-color: #0056b3;
}

.btn-secondary {
	background-color: #6c757d;
	color: white;
}

.btn-secondary:hover:not(:disabled) {
	background-color: #545b62;
}

.btn-success {
	background-color: #28a745;
	color: white;
}

.btn-success:hover:not(:disabled) {
	background-color: #218838;
}

.btn-outline-secondary {
	background-color: transparent;
	color: #6c757d;
	border: 1px solid #6c757d;
}

.btn-outline-secondary:hover:not(:disabled) {
	background-color: #6c757d;
	color: white;
}

.btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.fa-spin {
	animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* Seção de upload */
.upload-section {
	padding: 15px 20px;
	background-color: #fff3cd;
	border-bottom: 1px solid #ffeaa7;
}

.upload-preview h5 {
	margin: 0 0 10px 0;
	color: #856404;
	display: flex;
	align-items: center;
	gap: 8px;
}

.folder-preview {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 15px;
	padding: 8px 12px;
	background-color: white;
	border-radius: 4px;
	border: 1px solid #ffeaa7;
}

.file-count-badge {
	background-color: #007bff;
	color: white;
	padding: 2px 8px;
	border-radius: 12px;
	font-size: 12px;
	margin-left: auto;
}

.upload-actions {
	display: flex;
	gap: 10px;
}

.upload-progress {
	margin-top: 15px;
}

.upload-header {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10px;
	padding: 12px 15px;
	background-color: white;
	border-radius: 4px;
	border: 1px solid #ffeaa7;
	color: #856404;
	font-size: 14px;
	font-weight: 500;
	margin-bottom: 10px;
}

.progress {
	width: 100%;
	height: 20px;
	background-color: #e9ecef;
	border-radius: 10px;
	overflow: hidden;
	border: 1px solid #ffeaa7;
}

.progress-bar {
	height: 100%;
	background: linear-gradient(90deg, #007bff 0%, #0056b3 100%);
	transition: width 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-size: 12px;
	font-weight: bold;
	text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Resultado do upload */
.upload-result {
	padding: 15px 20px;
}

.alert {
	padding: 12px 15px;
	border-radius: 6px;
	display: flex;
	align-items: center;
	gap: 10px;
	cursor: pointer;
	transition: opacity 0.2s ease;
	position: relative;
}

.alert:hover {
	opacity: 0.9;
}

.alert-success {
	background-color: #d4edda;
	border: 1px solid #c3e6cb;
	color: #155724;
}

.alert-danger {
	background-color: #f8d7da;
	border: 1px solid #f5c6cb;
	color: #721c24;
}

.close-btn {
	position: absolute;
	right: 15px;
	background: none;
	border: none;
	font-size: 18px;
	cursor: pointer;
	color: inherit;
	opacity: 0.7;
}

.close-btn:hover {
	opacity: 1;
}

/* Estado vazio */
.empty-state {
	text-align: center;
	padding: 60px 20px;
	color: #666;
	background-color: #f8f9fa;
}

.empty-state i {
	font-size: 48px;
	margin-bottom: 16px;
	color: #ddd;
}

.empty-state p {
	margin: 8px 0;
}

.empty-state p:first-of-type {
	font-size: 16px;
	font-weight: 500;
}

.empty-state p:last-of-type {
	font-size: 14px;
	color: #999;
}

/* Container da árvore */
.tree-container {
	background-color: white;
}

.table-header {
	display: grid;
	grid-template-columns: 1fr 100px 100px 150px;
	gap: 10px;
	padding: 12px 10px;
	background-color: #f8f9fa;
	border-bottom: 2px solid #dee2e6;
	font-weight: bold;
	font-size: 14px;
}

/* Compatibilidade com componentes filhos */
.hover-row:hover {
	background-color: #f8f9fa !important;
}

.drag-over {
	background-color: #e3f2fd !important;
}
</style>
